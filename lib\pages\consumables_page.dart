import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/consumable.dart';
import '../cubits/consumables_cubit.dart';
import '../cubits/consumable_collections_cubit.dart';
import '../l10n/app_localizations.dart';
import '../widgets/dialogs/edit_consumable_dialog.dart';
import '../widgets/dialogs/add_consumable_dialog.dart';
import '../widgets/dialogs/save_collection_dialog.dart';
import '../widgets/dialogs/manage_collections_dialog.dart';
import '../constants/app_colors.dart';

class ConsumablesPage extends StatefulWidget {
  const ConsumablesPage({super.key});

  @override
  State<ConsumablesPage> createState() => _ConsumablesPageState();
}

class _ConsumablesPageState extends State<ConsumablesPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _searchQuery = '';
      }
    });

    if (_isSearching) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  List<Consumable> _getFilteredConsumables(List<Consumable> consumables) {
    if (_searchQuery.isEmpty) return consumables;

    return consumables.where((consumable) {
      return consumable.name
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          consumable.price.toString().contains(_searchQuery);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.offWhite,
      appBar: AppBar(
        title: _isSearching
            ? AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: _animationController,
                      curve: Curves.easeInOut,
                    )),
                    child: TextField(
                      controller: _searchController,
                      autofocus: true,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        hintText: '${localizations.search}...',
                        hintStyle: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                        border: InputBorder.none,
                        prefixIcon: const Icon(
                          Icons.search,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  );
                },
              )
            : Text(
                localizations.consumables,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
        backgroundColor: AppColors.primaryBlue,
        elevation: 0,
        centerTitle: true,
        actions: [
          // Search Toggle Button
          IconButton(
            onPressed: _toggleSearch,
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: Colors.white,
            ),
            tooltip: _isSearching ? localizations.cancel : localizations.search,
          ),
          // Load Collection Button
          if (!_isSearching)
            IconButton(
              onPressed: () => _showManageCollections(context),
              icon: const Icon(
                Icons.collections_bookmark,
                color: Colors.white,
              ),
              tooltip: localizations.manageCollections,
            ),
          // Save Collection Button
          if (!_isSearching)
            BlocBuilder<ConsumablesCubit, ConsumablesState>(
              builder: (context, state) {
                final canSave =
                    state.consumables.isNotEmpty && !state.hasCurrentCollection;
                return IconButton(
                  onPressed: canSave
                      ? () => _saveCurrentCollection(context, state.consumables)
                      : null,
                  icon: Icon(
                    state.hasCurrentCollection
                        ? Icons.bookmark
                        : Icons.bookmark_add,
                    color: canSave
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.5),
                  ),
                  tooltip: state.hasCurrentCollection
                      ? '${localizations.loadCollection}: ${state.currentCollectionName}'
                      : localizations.saveCollection,
                );
              },
            ),
        ],
      ),
      body: BlocBuilder<ConsumablesCubit, ConsumablesState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryBlue,
              ),
            );
          }

          if (state.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.errorRed,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${state.error}',
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.errorRed,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          if (state.consumables.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.construction_outlined,
                    size: 64,
                    color: AppColors.primaryBlue,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    localizations.noConsumablesFound,
                    style: const TextStyle(
                      fontSize: 18,
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    localizations.addFirstConsumable,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.neutralGrey,
                    ),
                  ),
                ],
              ),
            );
          }

          final filteredConsumables =
              _getFilteredConsumables(state.consumables);

          return Column(
            children: [
              // Current Collection Status (if any)
              if (state.hasCurrentCollection && !_isSearching)
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: AppColors.backgroundBlue,
                    borderRadius: BorderRadius.circular(12.0),
                    border: Border.all(
                      color: AppColors.primaryBlue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.bookmark,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.loadCollection,
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.primaryBlue,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              state.currentCollectionName!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.darkGrey,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => _clearCurrentCollection(context),
                        icon: const Icon(
                          Icons.close,
                          color: AppColors.primaryBlue,
                          size: 20,
                        ),
                        tooltip: localizations.cancel,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),

              // Search Results Info
              if (_isSearching && _searchQuery.isNotEmpty)
                Container(
                  margin: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 12.0),
                  decoration: BoxDecoration(
                    color: AppColors.backgroundBlue,
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(
                      color: AppColors.primaryBlue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.search,
                        color: AppColors.primaryBlue,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${filteredConsumables.length} ${localizations.items} found for "$_searchQuery"',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

              // Total Consumables Card
              Container(
                margin: const EdgeInsets.all(16.0),
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primaryBlue,
                      AppColors.primaryBlueDark,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.construction,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.totalConsumables,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'SAR ${context.read<ConsumablesCubit>().getTotalConsumablesPrice().toStringAsFixed(2)}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Consumables List
              Expanded(
                child: filteredConsumables.isEmpty && _searchQuery.isNotEmpty
                    ? _buildNoSearchResults(localizations)
                    : AnimatedList(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        initialItemCount: filteredConsumables.length,
                        itemBuilder: (context, index, animation) {
                          if (index >= filteredConsumables.length) {
                            return const SizedBox();
                          }
                          final consumable = filteredConsumables[index];
                          return SlideTransition(
                            position: animation.drive(
                              Tween<Offset>(
                                begin: const Offset(1.0, 0.0),
                                end: Offset.zero,
                              ).chain(CurveTween(curve: Curves.easeOut)),
                            ),
                            child: _buildEnhancedConsumableCard(
                              context,
                              consumable,
                              localizations,
                              index,
                            ),
                          );
                        },
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildNoSearchResults(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.lightGrey,
          ),
          const SizedBox(height: 16),
          Text(
            'No consumables found',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.neutralGrey,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search terms',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.lightGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedConsumableCard(
    BuildContext context,
    Consumable consumable,
    AppLocalizations localizations,
    int index,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Color(0xFFFAFAFA),
            ],
          ),
        ),
        child: ListTile(
          contentPadding: const EdgeInsets.all(16.0),
          leading: Hero(
            tag: 'consumable_${consumable.name}_$index',
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: AppColors.backgroundBlue,
                borderRadius: BorderRadius.circular(12.0),
              ),
              child: const Icon(
                Icons.shopping_bag_outlined,
                color: AppColors.primaryBlue,
                size: 24,
              ),
            ),
          ),
          title: Text(
            consumable.name,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: AppColors.darkGrey,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                'SAR ${consumable.price.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.primaryBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (_searchQuery.isNotEmpty &&
                  consumable.name
                      .toLowerCase()
                      .contains(_searchQuery.toLowerCase()))
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.warningOrange.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Match: $_searchQuery',
                    style: const TextStyle(
                      fontSize: 10,
                      color: AppColors.warningOrange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) =>
                        EditConsumableDialog(consumable: consumable),
                  );
                },
                icon: const Icon(
                  Icons.edit_outlined,
                  color: AppColors.primaryBlue,
                ),
                tooltip: localizations.editProduct,
              ),
              IconButton(
                onPressed: () {
                  _showDeleteConfirmation(context, consumable, localizations);
                },
                icon: const Icon(
                  Icons.delete_outline,
                  color: AppColors.errorRed,
                ),
                tooltip: localizations.delete,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Consumable consumable,
      AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.deleteConsumable),
        content: Text(localizations.confirmDeleteConsumable),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () async {
              final consumablesCubit = context.read<ConsumablesCubit>();
              try {
                await consumablesCubit.deleteConsumable(consumable);
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content:
                          Text(localizations.consumableDeletedSuccessfully),
                      backgroundColor: AppColors.successGreen,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: AppColors.errorRed,
                    ),
                  );
                }
              }
            },
            child: Text(
              localizations.delete,
              style: const TextStyle(color: AppColors.errorRed),
            ),
          ),
        ],
      ),
    );
  }

  void _saveCurrentCollection(
      BuildContext context, List<Consumable> consumables) {
    showSaveCollectionDialog(
      context: context,
      consumables: consumables,
      onSaved: () {
        // Clear the current consumables list after saving
        context.read<ConsumablesCubit>().clearConsumables();
      },
    );
  }

  void _showManageCollections(BuildContext context) {
    showManageCollectionsDialog(
      context: context,
      onCollectionLoaded: (consumables, collectionId, collectionName) {
        // Load the selected collection with tracking
        final consumablesCubit = context.read<ConsumablesCubit>();
        consumablesCubit.loadConsumablesFromCollection(
          consumables,
          collectionId,
          collectionName,
        );
      },
    );
  }

  void _clearCurrentCollection(BuildContext context) {
    final consumablesCubit = context.read<ConsumablesCubit>();
    consumablesCubit.clearCurrentCollection();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context).collectionLoaded),
        backgroundColor: AppColors.warningOrange,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showQuickAddDialog(BuildContext context) {
    final commonItems = [
      {'name': 'Cement Bag', 'price': 25.0},
      {'name': 'Steel Rod', 'price': 15.0},
      {'name': 'Paint Bucket', 'price': 45.0},
      {'name': 'Wire Roll', 'price': 35.0},
      {'name': 'Pipe Section', 'price': 20.0},
      {'name': 'Screws Pack', 'price': 12.0},
    ];

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundBlue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.flash_on,
                      color: AppColors.primaryBlue,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Text(
                      'Quick Add',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.darkGrey,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              const Text(
                'Select a common item to add quickly:',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.neutralGrey,
                ),
              ),

              const SizedBox(height: 16),

              // Quick items grid
              SizedBox(
                height: 200,
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: commonItems.length,
                  itemBuilder: (context, index) {
                    final item = commonItems[index];
                    return Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () async {
                          try {
                            final consumable = Consumable(
                              name: item['name'] as String,
                              price: item['price'] as double,
                            );

                            final consumablesCubit =
                                context.read<ConsumablesCubit>();
                            await consumablesCubit.addConsumable(consumable);

                            if (context.mounted) {
                              Navigator.of(context).pop();
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                      '${item['name']} added successfully'),
                                  backgroundColor: AppColors.successGreen,
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                            }
                          } catch (e) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Error: $e'),
                                  backgroundColor: AppColors.errorRed,
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                            }
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                item['name'] as String,
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.darkGrey,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'SAR ${(item['price'] as double).toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),

              // Custom add button
              SizedBox(
                width: double.infinity,
                child: TextButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    showDialog(
                      context: context,
                      builder: (context) => const AddConsumableDialog(),
                    );
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add Custom Item'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: const BorderSide(color: AppColors.primaryBlue),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
