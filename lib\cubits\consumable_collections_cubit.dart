import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/consumable_collection.dart';
import '../models/consumable.dart';

class ConsumableCollectionsCubit extends Cubit<List<ConsumableCollection>> {
  final Box<ConsumableCollection> _collectionsBox;

  ConsumableCollectionsCubit(this._collectionsBox) : super([]) {
    _loadCollections();
  }

  void _loadCollections() {
    final collections = _collectionsBox.values.toList();
    // Sort by updated date (most recent first)
    collections.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    emit(collections);
  }

  // Save current consumables as a new collection
  Future<void> saveCollection(String name, List<Consumable> consumables) async {
    if (name.trim().isEmpty) {
      throw Exception('Collection name cannot be empty');
    }

    if (consumables.isEmpty) {
      throw Exception('Cannot save empty collection');
    }

    // Check if collection name already exists
    final existingCollection = _collectionsBox.values
        .where(
            (collection) => collection.name.toLowerCase() == name.toLowerCase())
        .firstOrNull;

    if (existingCollection != null) {
      throw Exception('Collection with this name already exists');
    }

    // Create a deep copy of consumables to avoid reference issues
    final consumablesCopy = consumables
        .map((consumable) =>
            Consumable(name: consumable.name, price: consumable.price))
        .toList();

    final collection = ConsumableCollection(
      name: name.trim(),
      consumables: consumablesCopy,
    );

    await _collectionsBox.add(collection);
    _loadCollections();
  }

  // Load a collection (returns the consumables list)
  List<Consumable> loadCollection(String collectionId) {
    final collection =
        _collectionsBox.values.where((c) => c.id == collectionId).firstOrNull;

    if (collection == null) {
      throw Exception('Collection not found');
    }

    // Update the last accessed time
    final updatedCollection = collection.copyWith(updatedAt: DateTime.now());
    final index =
        _collectionsBox.values.toList().indexWhere((c) => c.id == collectionId);
    if (index != -1) {
      _collectionsBox.putAt(index, updatedCollection);
      _loadCollections();
    }

    // Return a deep copy of consumables
    return collection.consumables
        .map((consumable) =>
            Consumable(name: consumable.name, price: consumable.price))
        .toList();
  }

  // Delete a collection
  Future<void> deleteCollection(String collectionId) async {
    final index =
        _collectionsBox.values.toList().indexWhere((c) => c.id == collectionId);
    if (index != -1) {
      await _collectionsBox.deleteAt(index);
      _loadCollections();
    }
  }

  // Update collection name
  Future<void> updateCollectionName(String collectionId, String newName) async {
    if (newName.trim().isEmpty) {
      throw Exception('Collection name cannot be empty');
    }

    // Check if new name already exists (excluding current collection)
    final existingCollection = _collectionsBox.values
        .where((collection) =>
            collection.name.toLowerCase() == newName.toLowerCase() &&
            collection.id != collectionId)
        .firstOrNull;

    if (existingCollection != null) {
      throw Exception('Collection with this name already exists');
    }

    final index =
        _collectionsBox.values.toList().indexWhere((c) => c.id == collectionId);
    if (index != -1) {
      final collection = _collectionsBox.getAt(index);
      if (collection != null) {
        final updatedCollection = collection.copyWith(
          name: newName.trim(),
          updatedAt: DateTime.now(),
        );
        await _collectionsBox.putAt(index, updatedCollection);
        _loadCollections();
      }
    }
  }

  // Update entire collection (name and consumables)
  Future<void> updateCollection(String collectionId, String newName,
      List<Consumable> newConsumables) async {
    if (newName.trim().isEmpty) {
      throw Exception('Collection name cannot be empty');
    }

    if (newConsumables.isEmpty) {
      throw Exception('Cannot save empty collection');
    }

    // Check if new name already exists (excluding current collection)
    final existingCollection = _collectionsBox.values
        .where((collection) =>
            collection.name.toLowerCase() == newName.toLowerCase() &&
            collection.id != collectionId)
        .firstOrNull;

    if (existingCollection != null) {
      throw Exception('Collection with this name already exists');
    }

    final index =
        _collectionsBox.values.toList().indexWhere((c) => c.id == collectionId);
    if (index != -1) {
      final collection = _collectionsBox.getAt(index);
      if (collection != null) {
        // Create a deep copy of consumables to avoid reference issues
        final consumablesCopy = newConsumables
            .map((consumable) =>
                Consumable(name: consumable.name, price: consumable.price))
            .toList();

        final updatedCollection = collection.copyWith(
          name: newName.trim(),
          consumables: consumablesCopy,
          updatedAt: DateTime.now(),
        );
        await _collectionsBox.putAt(index, updatedCollection);
        _loadCollections();
      }
    }
  }

  // Get collection by ID
  ConsumableCollection? getCollection(String collectionId) {
    return _collectionsBox.values
        .where((c) => c.id == collectionId)
        .firstOrNull;
  }

  // Get collection statistics
  Map<String, dynamic> getStatistics() {
    final collections = _collectionsBox.values.toList();

    if (collections.isEmpty) {
      return {
        'totalCollections': 0,
        'totalItems': 0,
        'totalValue': 0.0,
        'averageItemsPerCollection': 0.0,
        'averageValuePerCollection': 0.0,
      };
    }

    final totalItems = collections.fold<int>(
        0, (sum, collection) => sum + collection.itemCount);
    final totalValue = collections.fold<double>(
        0.0, (sum, collection) => sum + collection.totalPrice);

    return {
      'totalCollections': collections.length,
      'totalItems': totalItems,
      'totalValue': totalValue,
      'averageItemsPerCollection': totalItems / collections.length,
      'averageValuePerCollection': totalValue / collections.length,
    };
  }

  // Search collections by name
  List<ConsumableCollection> searchCollections(String query) {
    if (query.trim().isEmpty) {
      return state;
    }

    final lowercaseQuery = query.toLowerCase();
    return state
        .where((collection) =>
            collection.name.toLowerCase().contains(lowercaseQuery))
        .toList();
  }

  // Clear all collections (for testing/reset purposes)
  Future<void> clearAllCollections() async {
    await _collectionsBox.clear();
    _loadCollections();
  }
}
