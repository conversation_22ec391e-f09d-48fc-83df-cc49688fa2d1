import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'supplier_payment_record.g.dart';

@HiveType(typeId: 21)
class SupplierPaymentRecord extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String supplierId;

  @HiveField(2)
  String invoiceId;

  @HiveField(3)
  double amount;

  @HiveField(4)
  DateTime timestamp;

  @HiveField(5)
  late int timestampSeconds;

  @HiveField(6)
  late int timestampMinutes;

  @HiveField(7)
  late int timestampDays;

  @HiveField(8, defaultValue: 0.0)
  double collectedAmount;

  SupplierPaymentRecord({
    String? id,
    required this.supplierId,
    required this.invoiceId,
    required this.amount,
    DateTime? timestamp,
    int? timestampSeconds,
    int? timestampMinutes,
    int? timestampDays,
    this.collectedAmount = 0.0,
  })  : id = id ?? const Uuid().v4(),
        timestamp = timestamp ?? DateTime.now() {
    // Calculate timestamp components if not provided
    final now = this.timestamp;
    this.timestampSeconds = timestampSeconds ?? now.second;
    this.timestampMinutes = timestampMinutes ?? now.minute;
    this.timestampDays = timestampDays ?? now.day;
  }

  // Convert to JSON for debugging/export
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'supplierId': supplierId,
      'invoiceId': invoiceId,
      'amount': amount,
      'timestamp': timestamp.toIso8601String(),
      'timestampSeconds': timestampSeconds,
      'timestampMinutes': timestampMinutes,
      'timestampDays': timestampDays,
      'collectedAmount': collectedAmount,
    };
  }

  // Create from JSON
  factory SupplierPaymentRecord.fromJson(Map<String, dynamic> json) {
    return SupplierPaymentRecord(
      id: json['id'],
      supplierId: json['supplierId'],
      invoiceId: json['invoiceId'],
      amount: json['amount'].toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
      timestampSeconds: json['timestampSeconds'],
      timestampMinutes: json['timestampMinutes'],
      timestampDays: json['timestampDays'],
      collectedAmount: json['collectedAmount']?.toDouble() ?? 0.0,
    );
  }
}
