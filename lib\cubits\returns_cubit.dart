import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/return.dart';
import '../models/invoice.dart';

class ReturnsCubit extends Cubit<List<Return>> {
  final Box<Return> _returnsBox;

  ReturnsCubit(this._returnsBox) : super(_returnsBox.values.toList());

  // Add a new return record
  Future<void> addReturn(Return returnRecord) async {
    try {
      await _returnsBox.add(returnRecord);
      emit(_returnsBox.values.toList());
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  // Get all returns for a specific invoice
  List<Return> getReturnsForInvoice(String invoiceId) {
    return _returnsBox.values
        .where((returnRecord) => returnRecord.invoiceId == invoiceId)
        .toList()
      ..sort((a, b) => b.returnDate.compareTo(a.returnDate)); // Most recent first
  }

  // Get all returns within a date range
  List<Return> getReturnsByDateRange(DateTime startDate, DateTime endDate) {
    return _returnsBox.values
        .where((returnRecord) =>
            returnRecord.returnDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
            returnRecord.returnDate.isBefore(endDate.add(const Duration(days: 1))))
        .toList()
      ..sort((a, b) => b.returnDate.compareTo(a.returnDate));
  }

  // Get total return amount for an invoice
  double getTotalReturnAmountForInvoice(String invoiceId) {
    return getReturnsForInvoice(invoiceId)
        .fold(0.0, (sum, returnRecord) => sum + returnRecord.totalReturnAmount);
  }

  // Get total returned quantity for a specific product in an invoice
  int getTotalReturnedQuantityForProduct(String invoiceId, String productId) {
    final returns = getReturnsForInvoice(invoiceId);
    int totalReturned = 0;
    
    for (final returnRecord in returns) {
      for (final item in returnRecord.items) {
        if (item.productId == productId) {
          totalReturned += item.returnedQuantity;
        }
      }
    }
    
    return totalReturned;
  }

  // Delete a return record
  Future<void> deleteReturn(String returnId) async {
    try {
      final returnRecord = _returnsBox.values.firstWhere((r) => r.id == returnId);
      await returnRecord.delete();
      emit(_returnsBox.values.toList());
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  // Get return statistics
  Map<String, dynamic> getReturnStatistics() {
    final allReturns = _returnsBox.values.toList();
    
    return {
      'totalReturns': allReturns.length,
      'totalReturnAmount': allReturns.fold(0.0, (sum, r) => sum + r.totalReturnAmount),
      'totalReturnedItems': allReturns.fold(0, (sum, r) => sum + r.totalReturnedQuantity),
      'averageReturnAmount': allReturns.isEmpty 
          ? 0.0 
          : allReturns.fold(0.0, (sum, r) => sum + r.totalReturnAmount) / allReturns.length,
    };
  }

  // Refresh the state
  void refresh() {
    emit(_returnsBox.values.toList());
  }
}
