// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'return.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ReturnAdapter extends TypeAdapter<Return> {
  @override
  final int typeId = 6;

  @override
  Return read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Return(
      id: fields[0] as String?,
      invoiceId: fields[1] as String,
      returnDate: fields[2] as DateTime,
      items: (fields[3] as List).cast<ReturnItem>(),
      totalReturnAmount: fields[4] as double,
      notes: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Return obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.invoiceId)
      ..writeByte(2)
      ..write(obj.returnDate)
      ..writeByte(3)
      ..write(obj.items)
      ..writeByte(4)
      ..write(obj.totalReturnAmount)
      ..writeByte(5)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReturnAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ReturnItemAdapter extends TypeAdapter<ReturnItem> {
  @override
  final int typeId = 7;

  @override
  ReturnItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ReturnItem(
      productId: fields[0] as String,
      productCategory: fields[1] as String,
      returnedQuantity: fields[2] as int,
      pricePerUnit: fields[3] as double,
      returnedAmount: fields[4] as double,
    );
  }

  @override
  void write(BinaryWriter writer, ReturnItem obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.productId)
      ..writeByte(1)
      ..write(obj.productCategory)
      ..writeByte(2)
      ..write(obj.returnedQuantity)
      ..writeByte(3)
      ..write(obj.pricePerUnit)
      ..writeByte(4)
      ..write(obj.returnedAmount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReturnItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
