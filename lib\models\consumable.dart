import 'package:hive/hive.dart';

part 'consumable.g.dart';

@HiveType(typeId: 4)
class Consumable extends HiveObject {
  @HiveField(0)
  String name;

  @HiveField(1)
  double price;

  Consumable({
    required this.name,
    required this.price,
  });

  // Convert to JSON for debugging/export
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'price': price,
    };
  }

  @override
  String toString() {
    return 'Consumable{name: $name, price: $price}';
  }
}
