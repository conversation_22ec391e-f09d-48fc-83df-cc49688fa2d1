import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import '../models/consumable.dart';

class ConsumablesState {
  final List<Consumable> consumables;
  final bool isLoading;
  final String? error;
  final String? currentCollectionId;
  final String? currentCollectionName;

  const ConsumablesState({
    this.consumables = const [],
    this.isLoading = false,
    this.error,
    this.currentCollectionId,
    this.currentCollectionName,
  });

  ConsumablesState copyWith({
    List<Consumable>? consumables,
    bool? isLoading,
    String? error,
    String? currentCollectionId,
    String? currentCollectionName,
    bool clearCollection = false,
  }) {
    return ConsumablesState(
      consumables: consumables ?? this.consumables,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentCollectionId: clearCollection
          ? null
          : (currentCollectionId ?? this.currentCollectionId),
      currentCollectionName: clearCollection
          ? null
          : (currentCollectionName ?? this.currentCollectionName),
    );
  }

  // Helper getters
  bool get hasCurrentCollection => currentCollectionId != null;
  bool get isCurrentCollectionModified {
    if (!hasCurrentCollection) return false;
    // This would need to be implemented by comparing current consumables with saved collection
    // For now, we'll assume any change means it's modified
    return true;
  }
}

class ConsumablesCubit extends Cubit<ConsumablesState> {
  final Box<Consumable> _consumablesBox;

  ConsumablesCubit(this._consumablesBox) : super(const ConsumablesState()) {
    loadConsumables();
  }

  void loadConsumables() {
    emit(state.copyWith(isLoading: true));
    try {
      final consumables = _consumablesBox.values.toList();
      emit(state.copyWith(
        consumables: consumables,
        isLoading: false,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> addConsumable(Consumable consumable) async {
    try {
      await _consumablesBox.add(consumable);
      loadConsumables();
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> updateConsumable(Consumable consumable) async {
    try {
      await consumable.save();
      loadConsumables();
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> deleteConsumable(Consumable consumable) async {
    try {
      await consumable.delete();
      loadConsumables();
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  double getTotalConsumablesPrice() {
    return state.consumables.fold<double>(
      0.0,
      (sum, consumable) => sum + consumable.price,
    );
  }

  // Clear all consumables from the current list
  Future<void> clearConsumables() async {
    try {
      await _consumablesBox.clear();
      emit(state.copyWith(
        consumables: [],
        clearCollection: true,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  // Set current collection info (called when loading a collection)
  void setCurrentCollection(String collectionId, String collectionName) {
    emit(state.copyWith(
      currentCollectionId: collectionId,
      currentCollectionName: collectionName,
    ));
  }

  // Clear current collection info (called when manually clearing or creating new list)
  void clearCurrentCollection() {
    emit(state.copyWith(clearCollection: true));
  }

  // Load consumables from a collection
  Future<void> loadConsumablesFromCollection(List<Consumable> consumables,
      String collectionId, String collectionName) async {
    try {
      // Clear current consumables
      await _consumablesBox.clear();

      // Add new consumables
      for (final consumable in consumables) {
        await _consumablesBox.add(consumable);
      }

      // Update state with collection info
      emit(state.copyWith(
        consumables: consumables,
        currentCollectionId: collectionId,
        currentCollectionName: collectionName,
        isLoading: false,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  // Check if current consumables match a specific collection
  bool doesCurrentMatchCollection(
      String collectionId, List<Consumable> collectionConsumables) {
    if (state.currentCollectionId != collectionId) return false;
    if (state.consumables.length != collectionConsumables.length) return false;

    // Simple comparison by name and price
    for (int i = 0; i < state.consumables.length; i++) {
      final current = state.consumables[i];
      final collection = collectionConsumables[i];
      if (current.name != collection.name ||
          current.price != collection.price) {
        return false;
      }
    }
    return true;
  }
}
