// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supplier_payment_record.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SupplierPaymentRecordAdapter extends TypeAdapter<SupplierPaymentRecord> {
  @override
  final int typeId = 21;

  @override
  SupplierPaymentRecord read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SupplierPaymentRecord(
      id: fields[0] as String?,
      supplierId: fields[1] as String,
      invoiceId: fields[2] as String,
      amount: fields[3] as double,
      timestamp: fields[4] as DateTime?,
      timestampSeconds: fields[5] as int?,
      timestampMinutes: fields[6] as int?,
      timestampDays: fields[7] as int?,
      collectedAmount: fields[8] == null ? 0.0 : fields[8] as double,
    );
  }

  @override
  void write(BinaryWriter writer, SupplierPaymentRecord obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.supplierId)
      ..writeByte(2)
      ..write(obj.invoiceId)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.timestampSeconds)
      ..writeByte(6)
      ..write(obj.timestampMinutes)
      ..writeByte(7)
      ..write(obj.timestampDays)
      ..writeByte(8)
      ..write(obj.collectedAmount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SupplierPaymentRecordAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
