import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../cubits/products_cubit.dart';
import '../models/product.dart';
import '../l10n/app_localizations.dart';
import '../widgets/dialogs/edit_product_dialog.dart';
import '../constants/app_colors.dart';

class ProductsPage extends StatefulWidget {
  const ProductsPage({super.key});

  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage> {
  bool _isLowStockExpanded = false;
  bool _isExpiringSoonExpanded = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: BlocBuilder<ProductsCubit, ProductsState>(
          builder: (context, state) {
            return Column(
              children: [
                Text(
                  AppLocalizations.of(context).productsTab,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
                Text(
                  '${state.products.length} منتج',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            );
          },
        ),
        backgroundColor: AppColors.primaryBlue,
        elevation: 0,
        centerTitle: true,
      ),
      body: BlocBuilder<ProductsCubit, ProductsState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: Column(
              children: [
                // Search Bar
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: AppLocalizations.of(context).searchProducts,
                      prefixIcon: const Icon(
                        Icons.search,
                        color: AppColors.primaryBlue,
                      ),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                context
                                    .read<ProductsCubit>()
                                    .searchProducts('');
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide:
                            const BorderSide(color: AppColors.neutralGrey),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                            color: AppColors.primaryBlue, width: 2),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                    ),
                    onChanged: (query) {
                      setState(() {}); // Update UI for clear button
                      context.read<ProductsCubit>().searchProducts(query);
                    },
                  ),
                ),

                // Products Content
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (state.searchQuery.isEmpty) ...[
                        // Low Stock Products
                        if (state.lowStockProducts.isNotEmpty) ...[
                          _buildSectionHeader(
                            context,
                            AppLocalizations.of(context).lowStock,
                            AppColors.warningOrange,
                            Icons.warning_amber,
                            state.lowStockProducts.length,
                            _isLowStockExpanded,
                            () => setState(() =>
                                _isLowStockExpanded = !_isLowStockExpanded),
                          ),
                          const SizedBox(height: 12),
                          _buildProductsList(
                            context,
                            state.lowStockProducts,
                            _isLowStockExpanded,
                            AppColors.warningOrange,
                          ),
                          const SizedBox(height: 24),
                        ],

                        // Expiring Soon Products
                        if (state.expiringSoonProducts.isNotEmpty) ...[
                          _buildSectionHeader(
                            context,
                            AppLocalizations.of(context).expiringSoon,
                            AppColors.errorRed,
                            Icons.schedule,
                            state.expiringSoonProducts.length,
                            _isExpiringSoonExpanded,
                            () => setState(() => _isExpiringSoonExpanded =
                                !_isExpiringSoonExpanded),
                          ),
                          const SizedBox(height: 12),
                          _buildProductsList(
                            context,
                            state.expiringSoonProducts,
                            _isExpiringSoonExpanded,
                            AppColors.errorRed,
                          ),
                          const SizedBox(height: 24),
                        ],

                        // All Products
                        _buildSectionHeader(
                          context,
                          AppLocalizations.of(context).allProducts,
                          AppColors.primaryBlue,
                          Icons.inventory_2,
                          state.products.length,
                          true, // Always expanded
                          null, // No toggle
                        ),
                        const SizedBox(height: 12),
                        _buildProductsList(context, state.products, true,
                            AppColors.primaryBlue),
                      ] else ...[
                        // Search Results
                        _buildSectionHeader(
                          context,
                          AppLocalizations.of(context).searchResults,
                          AppColors.primaryBlue,
                          Icons.search,
                          state.filteredProducts.length,
                          true,
                          null,
                        ),
                        const SizedBox(height: 12),
                        _buildProductsList(context, state.filteredProducts,
                            true, AppColors.primaryBlue),
                      ],
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Products Overview Summary

  Widget _buildFinancialItem(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStatusCard(
    String title,
    String value,
    IconData icon,
    Color color,
    bool hasAlert,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Stack(
            children: [
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              if (hasAlert)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Section Header with expand/collapse functionality
  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    Color color,
    IconData icon,
    int count,
    bool isExpanded,
    VoidCallback? onToggle,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  '$count منتج',
                  style: TextStyle(
                    fontSize: 12,
                    color: color.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          if (onToggle != null)
            IconButton(
              onPressed: onToggle,
              icon: Icon(
                isExpanded ? Icons.expand_less : Icons.expand_more,
                color: color,
              ),
              tooltip: isExpanded ? 'إخفاء' : 'عرض الكل',
            ),
        ],
      ),
    );
  }

  // Enhanced Products List with full-width cards
  Widget _buildProductsList(
    BuildContext context,
    List<Product> products,
    bool isExpanded,
    Color accentColor,
  ) {
    if (products.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 48,
              color: AppColors.neutralGrey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد منتجات',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.neutralGrey.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    // Limit products if not expanded (show first 3)
    final displayProducts = isExpanded ? products : products.take(3).toList();

    return Column(
      children: [
        ...displayProducts.map((product) => _buildFullWidthProductCard(
              context,
              product,
              accentColor,
            )),
        if (!isExpanded && products.length > 3)
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: TextButton.icon(
              onPressed: () {
                // This will be handled by the parent's toggle function
              },
              icon: const Icon(Icons.expand_more),
              label: Text('عرض ${products.length - 3} منتجات أخرى'),
              style: TextButton.styleFrom(
                foregroundColor: accentColor,
              ),
            ),
          ),
      ],
    );
  }

  // Compact Product Card with essential details
  Widget _buildFullWidthProductCard(
    BuildContext context,
    Product product,
    Color accentColor,
  ) {
    final isLowStock = product.isLowStock;
    final isExpiringSoon = product.isExpiringSoon;
    final totalValue = product.pricePerUnit * product.quantity;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _showEditProductDialog(context, product),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: accentColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // Header Row
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: accentColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.inventory_2,
                        color: accentColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            product.category,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.darkGrey,
                            ),
                          ),
                          if (isLowStock || isExpiringSoon) ...[
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                if (isLowStock)
                                  _buildStatusBadge(
                                    'مخزون منخفض',
                                    Icons.warning_amber,
                                    AppColors.warningOrange,
                                  ),
                                if (isLowStock && isExpiringSoon)
                                  const SizedBox(width: 6),
                                if (isExpiringSoon)
                                  _buildStatusBadge(
                                    'ينتهي قريباً',
                                    Icons.schedule,
                                    AppColors.errorRed,
                                  ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    const Icon(
                      Icons.chevron_right,
                      color: AppColors.neutralGrey,
                      size: 20,
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Details Row
                Row(
                  children: [
                    Expanded(
                      child: _buildCompactDetailItem(
                        'الكمية',
                        product.quantity.toString(),
                        isLowStock
                            ? AppColors.warningOrange
                            : AppColors.primaryBlue,
                      ),
                    ),
                    Expanded(
                      child: _buildCompactDetailItem(
                        'سعر البيع',
                        '${product.pricePerUnit.toStringAsFixed(2)} ر.س',
                        AppColors.successGreen,
                      ),
                    ),
                    Expanded(
                      child: _buildCompactDetailItem(
                        'القيمة',
                        '${totalValue.toStringAsFixed(0)} ر.س',
                        AppColors.primaryBlue,
                      ),
                    ),
                    Expanded(
                      child: _buildCompactDetailItem(
                        'الانتهاء',
                        _formatShortDate(product.expirationDate),
                        isExpiringSoon
                            ? AppColors.errorRed
                            : AppColors.neutralGrey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 10,
            color: color,
          ),
          const SizedBox(width: 3),
          Text(
            text,
            style: TextStyle(
              fontSize: 9,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactDetailItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: color.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _showEditProductDialog(BuildContext context, Product product) {
    showEditProductDialog(context, product);
  }

  String _formatShortDate(DateTime date) {
    final formatter = DateFormat('dd/MM');
    return formatter.format(date);
  }
}
