import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/supplier.dart';
import '../../models/supplier_invoice.dart';
import '../../models/product.dart';
import '../../cubits/suppliers_cubit.dart';
import '../../cubits/supplier_invoices_cubit.dart';
import '../../cubits/products_cubit.dart';
import '../../constants/app_colors.dart';
import '../../pages/supplier_invoice_preview_page.dart';

void showAddSupplierInvoiceDialog({
  required BuildContext context,
}) {
  showDialog(
    context: context,
    builder: (context) => const AddSupplierInvoiceDialog(),
  );
}

class AddSupplierInvoiceDialog extends StatefulWidget {
  const AddSupplierInvoiceDialog({super.key});

  @override
  State<AddSupplierInvoiceDialog> createState() =>
      _AddSupplierInvoiceDialogState();
}

class _AddSupplierInvoiceDialogState extends State<AddSupplierInvoiceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _invoiceNumberController = TextEditingController();
  final _notesController = TextEditingController();

  Supplier? _selectedSupplier;
  DateTime _selectedDate = DateTime.now();
  List<SupplierInvoiceItem> _invoiceItems = [];
  double _totalAmount = 0.0;

  @override
  void initState() {
    super.initState();
    // Set the automatic invoice number when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setAutomaticInvoiceNumber();
    });
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _setAutomaticInvoiceNumber() {
    final nextInvoiceNumber =
        context.read<SupplierInvoicesCubit>().generateNextInvoiceNumber();
    _invoiceNumberController.text = nextInvoiceNumber;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.backgroundBlue,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.receipt_long,
                    color: AppColors.primaryBlue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'إضافة فاتورة مورد',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Form Content
            Expanded(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Supplier Selection
                      _buildSupplierSelection(),
                      const SizedBox(height: 16),

                      // Invoice Number and Date
                      Row(
                        children: [
                          Expanded(child: _buildInvoiceNumberField()),
                          const SizedBox(width: 16),
                          Expanded(child: _buildDateField()),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Products Section
                      _buildProductsSection(),
                      const SizedBox(height: 16),

                      // Notes Field

                      // Total Amount Display
                      _buildTotalAmountDisplay(),
                    ],
                  ),
                ),
              ),
            ),

            // Action Buttons
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.neutralGrey,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'إلغاء',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _addSupplierInvoice,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'معاينة الفاتورة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplierSelection() {
    return BlocBuilder<SuppliersCubit, SuppliersState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المورد *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.darkGrey,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.neutralGrey),
                borderRadius: BorderRadius.circular(12),
              ),
              child: DropdownButtonFormField<Supplier>(
                value: _selectedSupplier,
                decoration: const InputDecoration(
                  hintText: 'اختر المورد',
                  prefixIcon: Icon(
                    Icons.business,
                    color: AppColors.primaryBlue,
                  ),
                  border: InputBorder.none,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                items: state.suppliers.map((supplier) {
                  return DropdownMenuItem<Supplier>(
                    value: supplier,
                    child: Text(supplier.name),
                  );
                }).toList(),
                onChanged: (supplier) {
                  setState(() {
                    _selectedSupplier = supplier;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار المورد';
                  }
                  return null;
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInvoiceNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'رقم الفاتورة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.darkGrey,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.successGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: AppColors.successGreen.withValues(alpha: 0.3),
                ),
              ),
              child: const Text(
                'تلقائي',
                style: TextStyle(
                  fontSize: 10,
                  color: AppColors.successGreen,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _invoiceNumberController,
          readOnly: true,
          decoration: InputDecoration(
            hintText: 'رقم الفاتورة التلقائي',
            prefixIcon: const Icon(
              Icons.auto_awesome,
              color: AppColors.primaryBlue,
            ),
            suffixIcon: IconButton(
              icon: const Icon(
                Icons.refresh,
                color: AppColors.primaryBlue,
                size: 20,
              ),
              onPressed: _setAutomaticInvoiceNumber,
              tooltip: 'تحديث الرقم التلقائي',
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.neutralGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide:
                  const BorderSide(color: AppColors.primaryBlue, width: 2),
            ),
            filled: true,
            fillColor: AppColors.backgroundBlue.withValues(alpha: 0.1),
          ),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.primaryBlue,
          ),
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تاريخ الفاتورة *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.darkGrey,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.neutralGrey),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  color: AppColors.primaryBlue,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: const TextStyle(fontSize: 16),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProductsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'المنتجات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.darkGrey,
              ),
            ),
            ElevatedButton.icon(
              onPressed: _addProduct,
              icon: const Icon(Icons.add, size: 16),
              label: const Text('إضافة منتج'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          constraints: const BoxConstraints(maxHeight: 200),
          child: _invoiceItems.isEmpty
              ? Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: AppColors.neutralGrey.withValues(alpha: 0.3)),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Center(
                    child: Text(
                      'لم يتم إضافة منتجات بعد',
                      style: TextStyle(
                        color: AppColors.neutralGrey,
                        fontSize: 14,
                      ),
                    ),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  itemCount: _invoiceItems.length,
                  itemBuilder: (context, index) {
                    final item = _invoiceItems[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.white,
                              AppColors.backgroundBlue.withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: ListTile(
                          contentPadding: const EdgeInsets.all(12),
                          leading: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color:
                                  AppColors.primaryBlue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: AppColors.primaryBlue
                                    .withValues(alpha: 0.3),
                              ),
                            ),
                            child: const Icon(
                              Icons.inventory_2,
                              color: AppColors.primaryBlue,
                              size: 20,
                            ),
                          ),
                          title: Text(
                            item.product.category,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: AppColors.darkGrey,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.numbers,
                                    size: 14,
                                    color: AppColors.primaryBlue,
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      'الكمية: ${item.quantity}',
                                      style: const TextStyle(
                                        fontSize: 13,
                                        color: AppColors.primaryBlue,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 2),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.attach_money,
                                    size: 14,
                                    color: AppColors.warningOrange,
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      'سعر الوحدة: ${item.unitCost.toStringAsFixed(2)} ريال',
                                      style: const TextStyle(
                                        fontSize: 13,
                                        color: AppColors.warningOrange,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          trailing: SizedBox(
                            width: 90,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 4,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.successGreen
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: AppColors.successGreen
                                          .withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: Text(
                                    '${item.totalPrice.toStringAsFixed(2)} ريال',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 9,
                                      color: AppColors.successGreen,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                const SizedBox(height: 1),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    GestureDetector(
                                      onTap: () => _editProduct(index),
                                      child: Container(
                                        padding: const EdgeInsets.all(3),
                                        decoration: BoxDecoration(
                                          color: AppColors.primaryBlue
                                              .withValues(alpha: 0.1),
                                          borderRadius:
                                              BorderRadius.circular(3),
                                        ),
                                        child: const Icon(
                                          Icons.edit,
                                          color: AppColors.primaryBlue,
                                          size: 12,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 3),
                                    GestureDetector(
                                      onTap: () => _confirmRemoveProduct(index),
                                      child: Container(
                                        padding: const EdgeInsets.all(3),
                                        decoration: BoxDecoration(
                                          color: AppColors.errorRed
                                              .withValues(alpha: 0.1),
                                          borderRadius:
                                              BorderRadius.circular(3),
                                        ),
                                        child: const Icon(
                                          Icons.delete_outline,
                                          color: AppColors.errorRed,
                                          size: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildTotalAmountDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBlue.withValues(alpha: 0.1),
            AppColors.accentCyan.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Expanded(
            flex: 2,
            child: Text(
              'المبلغ الإجمالي:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              '${_totalAmount.toStringAsFixed(2)} ريال',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.warningOrange,
              ),
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primaryBlue,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.darkGrey,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _addProduct() {
    showDialog(
      context: context,
      builder: (context) => _AddProductDialog(
        onProductAdded: (product, quantity, unitCost) {
          setState(() {
            final totalPrice = quantity * unitCost;
            _invoiceItems.add(SupplierInvoiceItem(
              product: product,
              quantity: quantity,
              totalPrice: totalPrice,
              unitCost: unitCost,
            ));
            _calculateTotal();
          });
        },
      ),
    );
  }

  void _editProduct(int index) {
    final item = _invoiceItems[index];
    showDialog(
      context: context,
      builder: (context) => _EditProductDialog(
        item: item,
        onProductUpdated: (product, quantity, unitCost) {
          setState(() {
            final totalPrice = quantity * unitCost;
            _invoiceItems[index] = SupplierInvoiceItem(
              product: product,
              quantity: quantity,
              totalPrice: totalPrice,
              unitCost: unitCost,
            );
            _calculateTotal();
          });
        },
      ),
    );
  }

  void _confirmRemoveProduct(int index) {
    final item = _invoiceItems[index];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.errorRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.warning_amber,
                color: AppColors.errorRed,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.errorRed,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'هل أنت متأكد من حذف المنتج التالي من الفاتورة؟',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.darkGrey,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.backgroundBlue.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المنتج: ${item.product.category}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'الكمية: ${item.quantity}',
                    style: const TextStyle(color: AppColors.darkGrey),
                  ),
                  Text(
                    'المبلغ: ${item.totalPrice.toStringAsFixed(2)} ريال',
                    style: const TextStyle(color: AppColors.darkGrey),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.neutralGrey,
            ),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _removeProduct(index);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _removeProduct(int index) {
    setState(() {
      _invoiceItems.removeAt(index);
      _calculateTotal();
    });
  }

  void _calculateTotal() {
    _totalAmount =
        _invoiceItems.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  void _addSupplierInvoice() {
    if (_formKey.currentState!.validate()) {
      if (_selectedSupplier == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى اختيار المورد'),
            backgroundColor: AppColors.errorRed,
          ),
        );
        return;
      }

      if (_invoiceItems.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى إضافة منتج واحد على الأقل'),
            backgroundColor: AppColors.errorRed,
          ),
        );
        return;
      }

      // Close the dialog and navigate to preview page
      Navigator.pop(context);

      // Navigate to supplier invoice preview page
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => SupplierInvoicePreviewPage(
            supplier: _selectedSupplier!,
            selectedItems: List.from(_invoiceItems),
            date: _selectedDate,
            invoiceNumber: _invoiceNumberController.text.trim(),
            notes: _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
          ),
        ),
      );
    }
  }
}

class _AddProductDialog extends StatefulWidget {
  final Function(Product, int, double) onProductAdded;

  const _AddProductDialog({required this.onProductAdded});

  @override
  State<_AddProductDialog> createState() => _AddProductDialogState();
}

class _AddProductDialogState extends State<_AddProductDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _unitCostController = TextEditingController();
  final _newProductNameController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _expiryDateController = TextEditingController();

  Product? _selectedProduct;
  bool _isCreatingNewProduct = false;
  DateTime? _selectedExpiryDate;

  @override
  void dispose() {
    _quantityController.dispose();
    _unitCostController.dispose();
    _newProductNameController.dispose();
    _sellingPriceController.dispose();
    _expiryDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            // Header
            const Text(
              'إضافة منتج للفاتورة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: 20),

            // Toggle between existing and new product
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _isCreatingNewProduct = false;
                        _selectedProduct = null;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: !_isCreatingNewProduct
                          ? AppColors.primaryBlue
                          : AppColors.neutralGrey.withValues(alpha: 0.3),
                      foregroundColor: !_isCreatingNewProduct
                          ? Colors.white
                          : AppColors.neutralGrey,
                    ),
                    child: const Text('منتج موجود'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _isCreatingNewProduct = true;
                        _selectedProduct = null;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isCreatingNewProduct
                          ? AppColors.primaryBlue
                          : AppColors.neutralGrey.withValues(alpha: 0.3),
                      foregroundColor: _isCreatingNewProduct
                          ? Colors.white
                          : AppColors.neutralGrey,
                    ),
                    child: const Text('منتج جديد'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Form Content
            Expanded(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      if (!_isCreatingNewProduct) ...[
                        // Existing Product Selection
                        BlocBuilder<ProductsCubit, ProductsState>(
                          builder: (context, state) {
                            return DropdownButtonFormField<Product>(
                              value: _selectedProduct,
                              decoration: InputDecoration(
                                labelText: 'المنتج *',
                                prefixIcon: const Icon(
                                  Icons.inventory,
                                  color: AppColors.primaryBlue,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              items: state.products.map((product) {
                                return DropdownMenuItem<Product>(
                                  value: product,
                                  child: Text(product.category),
                                );
                              }).toList(),
                              onChanged: (product) {
                                setState(() {
                                  _selectedProduct = product;
                                });
                              },
                              validator: (value) {
                                if (!_isCreatingNewProduct && value == null) {
                                  return 'يرجى اختيار المنتج';
                                }
                                return null;
                              },
                            );
                          },
                        ),
                      ] else ...[
                        // New Product Creation Fields
                        TextFormField(
                          controller: _newProductNameController,
                          decoration: InputDecoration(
                            labelText: 'اسم المنتج *',
                            prefixIcon: const Icon(
                              Icons.inventory,
                              color: AppColors.primaryBlue,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          validator: (value) {
                            if (_isCreatingNewProduct &&
                                (value == null || value.isEmpty)) {
                              return 'يرجى إدخال اسم المنتج';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        TextFormField(
                          controller: _sellingPriceController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'سعر البيع *',
                            prefixIcon: const Icon(
                              Icons.sell,
                              color: AppColors.primaryBlue,
                            ),
                            suffixText: 'ريال',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          validator: (value) {
                            if (_isCreatingNewProduct &&
                                (value == null || value.isEmpty)) {
                              return 'يرجى إدخال سعر البيع';
                            }
                            if (_isCreatingNewProduct &&
                                double.tryParse(value!) == null) {
                              return 'يرجى إدخال سعر صحيح';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        InkWell(
                          onTap: _selectExpiryDate,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.neutralGrey),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.calendar_today,
                                  color: AppColors.primaryBlue,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  _selectedExpiryDate != null
                                      ? 'تاريخ الانتهاء: ${_selectedExpiryDate!.day}/${_selectedExpiryDate!.month}/${_selectedExpiryDate!.year}'
                                      : 'تاريخ الانتهاء (اختياري)',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: _selectedExpiryDate != null
                                        ? AppColors.darkGrey
                                        : AppColors.neutralGrey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 16),

                      // Quantity Field
                      TextFormField(
                        controller: _quantityController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'الكمية *',
                          prefixIcon: const Icon(
                            Icons.numbers,
                            color: AppColors.primaryBlue,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الكمية';
                          }
                          if (int.tryParse(value) == null ||
                              int.parse(value) <= 0) {
                            return 'يرجى إدخال كمية صحيحة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Unit Cost Field
                      TextFormField(
                        controller: _unitCostController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'سعر الوحدة *',
                          prefixIcon: const Icon(
                            Icons.attach_money,
                            color: AppColors.primaryBlue,
                          ),
                          suffixText: 'ريال',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال سعر الوحدة';
                          }
                          if (double.tryParse(value) == null ||
                              double.parse(value) <= 0) {
                            return 'يرجى إدخال سعر صحيح';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Action Buttons
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.neutralGrey,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _addProduct,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('إضافة'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _selectExpiryDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 years
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primaryBlue,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.darkGrey,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedExpiryDate) {
      setState(() {
        _selectedExpiryDate = picked;
      });
    }
  }

  void _addProduct() async {
    if (_formKey.currentState!.validate()) {
      final quantity = int.parse(_quantityController.text);
      final unitCost = double.parse(_unitCostController.text);

      Product productToAdd;

      if (_isCreatingNewProduct) {
        // Create new product
        final sellingPrice = double.parse(_sellingPriceController.text);
        productToAdd = Product(
          category: _newProductNameController.text.trim(),
          pricePerUnit: sellingPrice,
          purchasePrice: unitCost,
          quantity: 0, // Will be updated by supplier invoice
          expirationDate: _selectedExpiryDate ??
              DateTime.now().add(const Duration(days: 365)),
        );

        // Add the new product to the products cubit
        try {
          await context.read<ProductsCubit>().addProduct(productToAdd);
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في إضافة المنتج: $e'),
                backgroundColor: AppColors.errorRed,
              ),
            );
            return;
          }
        }
      } else {
        // Use existing product
        productToAdd = _selectedProduct!;
      }

      widget.onProductAdded(productToAdd, quantity, unitCost);
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }
}

class _EditProductDialog extends StatefulWidget {
  final SupplierInvoiceItem item;
  final Function(Product, int, double) onProductUpdated;

  const _EditProductDialog({
    required this.item,
    required this.onProductUpdated,
  });

  @override
  State<_EditProductDialog> createState() => _EditProductDialogState();
}

class _EditProductDialogState extends State<_EditProductDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _quantityController;
  late final TextEditingController _unitCostController;

  @override
  void initState() {
    super.initState();
    _quantityController =
        TextEditingController(text: widget.item.quantity.toString());
    _unitCostController =
        TextEditingController(text: widget.item.unitCost.toStringAsFixed(2));
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _unitCostController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundBlue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.edit,
                      color: AppColors.primaryBlue,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'تعديل المنتج',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Product Info (Read-only)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.backgroundBlue.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.primaryBlue.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.inventory_2,
                      color: AppColors.primaryBlue,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.item.product.category,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Quantity Field
              TextFormField(
                controller: _quantityController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'الكمية *',
                  prefixIcon: const Icon(
                    Icons.numbers,
                    color: AppColors.primaryBlue,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                        color: AppColors.primaryBlue, width: 2),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال الكمية';
                  }
                  if (int.tryParse(value) == null || int.parse(value) <= 0) {
                    return 'يرجى إدخال كمية صحيحة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Unit Cost Field
              TextFormField(
                controller: _unitCostController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'سعر الوحدة *',
                  prefixIcon: const Icon(
                    Icons.attach_money,
                    color: AppColors.primaryBlue,
                  ),
                  suffixText: 'ريال',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                        color: AppColors.primaryBlue, width: 2),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال سعر الوحدة';
                  }
                  if (double.tryParse(value) == null ||
                      double.parse(value) <= 0) {
                    return 'يرجى إدخال سعر صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.neutralGrey,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _updateProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('تحديث'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _updateProduct() {
    if (_formKey.currentState!.validate()) {
      final quantity = int.parse(_quantityController.text);
      final unitCost = double.parse(_unitCostController.text);

      widget.onProductUpdated(widget.item.product, quantity, unitCost);
      Navigator.pop(context);
    }
  }
}
