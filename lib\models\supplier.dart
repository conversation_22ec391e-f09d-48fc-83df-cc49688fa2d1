import 'package:hive/hive.dart';

part 'supplier.g.dart';

@HiveType(typeId: 9)
class Supplier extends HiveObject {
  @HiveField(0)
  String name;

  @HiveField(1)
  String phoneNumber;

  @HiveField(2)
  double totalPaid;

  @HiveField(3)
  double remainingBalance;

  @HiveField(4)
  String? address;

  @HiveField(5)
  String? email;

  Supplier({
    required this.name,
    required this.phoneNumber,
    this.totalPaid = 0.0,
    this.remainingBalance = 0.0,
    this.address,
    this.email,
  });

  // Convert to JSON for debugging/export
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'totalPaid': totalPaid,
      'remainingBalance': remainingBalance,
      'address': address,
      'email': email,
    };
  }

  @override
  String toString() {
    return 'Supplier{name: $name, phoneNumber: $phoneNumber, totalPaid: $totalPaid, remainingBalance: $remainingBalance}';
  }
}
