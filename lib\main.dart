import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'models/product.dart';
import 'models/customer.dart';
import 'models/invoice.dart';
import 'models/supplier.dart';
import 'models/supplier_invoice.dart';
import 'models/consumable.dart';
import 'models/return.dart';
import 'models/consumable_collection.dart';
import 'models/payment_record.dart';
import 'utils/hive_migration.dart';
import 'l10n/app_localizations_ar.dart';
import 'l10n/app_localizations_en.dart';
import 'app.dart';

void main() async {
  // Initialize the splash screen and get the binding
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

  // Keep the splash screen visible while we initialize Hive and load data
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Initialize Hive
  await Hive.initFlutter();

  // Register adapters
  Hive.registerAdapter(ProductAdapter());
  Hive.registerAdapter(CustomerAdapter());
  Hive.registerAdapter(InvoiceItemAdapter());
  Hive.registerAdapter(InvoiceAdapter());
  Hive.registerAdapter(SupplierAdapter());
  Hive.registerAdapter(SupplierInvoiceItemAdapter());
  Hive.registerAdapter(SupplierInvoiceAdapter());
  Hive.registerAdapter(ConsumableAdapter());
  Hive.registerAdapter(ReturnAdapter());
  Hive.registerAdapter(ReturnItemAdapter());
  Hive.registerAdapter(ConsumableCollectionAdapter());
  Hive.registerAdapter(PaymentRecordAdapter());

  // Open boxes with error handling for type casting issues
  final Box<Product> productsBox = await Hive.openBox<Product>('products');
  final Box<Customer> customersBox = await Hive.openBox<Customer>('customers');

  // Handle potential type casting errors in invoices box
  late Box<Invoice> invoicesBox;
  try {
    invoicesBox = await Hive.openBox<Invoice>('invoices');
  } catch (e) {
    // If there's a type casting error, clear the invoices data and try again
    print('Type casting error detected in invoices. Clearing invoices data...');
    await HiveMigration.clearInvoicesData();
    invoicesBox = await Hive.openBox<Invoice>('invoices');
  }

  final Box<Supplier> suppliersBox = await Hive.openBox<Supplier>('suppliers');
  final Box<SupplierInvoice> supplierInvoicesBox =
      await Hive.openBox<SupplierInvoice>('supplier_invoices');
  final Box<Consumable> consumablesBox =
      await Hive.openBox<Consumable>('consumables');
  final Box<Return> returnsBox = await Hive.openBox<Return>('returns');
  final Box<ConsumableCollection> collectionsBox =
      await Hive.openBox<ConsumableCollection>('consumable_collections');
  final Box<PaymentRecord> paymentRecordsBox =
      await Hive.openBox<PaymentRecord>('payment_records');

  // Run data migration if needed
  await HiveMigration.migrateInvoiceData();

  // Migrate existing products to include purchase price if they don't have it
  await _migrateProductsForPurchasePrice(productsBox);

  // Initialize localization classes to avoid null errors
  AppLocalizationsAr();
  AppLocalizationsEn();

  // Remove the splash screen once everything is loaded
  FlutterNativeSplash.remove();

  runApp(MyApp(
    productsBox: productsBox,
    customersBox: customersBox,
    invoicesBox: invoicesBox,
    suppliersBox: suppliersBox,
    supplierInvoicesBox: supplierInvoicesBox,
    consumablesBox: consumablesBox,
    returnsBox: returnsBox,
    collectionsBox: collectionsBox,
    paymentRecordsBox: paymentRecordsBox,
  ));
}

// Migration function to add purchase price to existing products
Future<void> _migrateProductsForPurchasePrice(Box<Product> productsBox) async {
  try {
    final products = productsBox.values.toList();
    bool needsMigration = false;

    for (final product in products) {
      // Check if the product has a purchase price field
      // If it doesn't exist or is null, we need to migrate
      try {
        // Try to access the purchase price
        final _ = product.purchasePrice;
      } catch (e) {
        // If accessing purchasePrice throws an error, it means the field doesn't exist
        needsMigration = true;
        break;
      }
    }

    if (needsMigration) {
      // Clear the box and recreate products with purchase price
      final productData = <Map<String, dynamic>>[];

      // Extract data from existing products
      for (final product in products) {
        productData.add({
          'category': product.category,
          'quantity': product.quantity,
          'pricePerUnit': product.pricePerUnit,
          'expirationDate': product.expirationDate,
          'purchasePrice':
              product.pricePerUnit, // Default to 70% of selling price
        });
      }

      // Clear the box
      await productsBox.clear();

      // Recreate products with purchase price
      for (final data in productData) {
        final newProduct = Product(
          category: data['category'],
          quantity: data['quantity'],
          pricePerUnit: data['pricePerUnit'],
          expirationDate: data['expirationDate'],
          purchasePrice: data['purchasePrice'],
        );
        await productsBox.add(newProduct);
      }
    }
  } catch (e) {
    // If migration fails, continue without it
    // The app will still work, but new products will need purchase price
    // Log migration failure silently
  }
}
