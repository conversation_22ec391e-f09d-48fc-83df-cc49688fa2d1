import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/invoice.dart';
import '../../l10n/app_localizations.dart';
import '../../constants/app_colors.dart';

class InvoiceReturnDialog extends StatefulWidget {
  final Invoice invoice;
  final Function(Map<InvoiceItem, int>) onReturnConfirmed;

  const InvoiceReturnDialog({
    super.key,
    required this.invoice,
    required this.onReturnConfirmed,
  });

  @override
  State<InvoiceReturnDialog> createState() => _InvoiceReturnDialogState();
}

class _InvoiceReturnDialogState extends State<InvoiceReturnDialog> {
  final Map<InvoiceItem, int> returnQuantities = {};
  final Map<InvoiceItem, TextEditingController> controllers = {};

  @override
  void initState() {
    super.initState();
    // Initialize controllers and return quantities
    for (final item in widget.invoice.items) {
      controllers[item] = TextEditingController();
      returnQuantities[item] = 0;
    }
  }

  @override
  void dispose() {
    for (final controller in controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  double get totalReturnAmount {
    double total = 0.0;
    for (final item in widget.invoice.items) {
      final returnQty = returnQuantities[item] ?? 0;
      if (returnQty > 0) {
        final pricePerUnit = item.totalPrice / item.quantity;
        total += pricePerUnit * returnQty;
      }
    }
    return total;
  }

  bool get hasValidReturns {
    return returnQuantities.values.any((qty) => qty > 0);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.0),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              AppColors.backgroundBlue,
            ],
          ),
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20.0),
                    topRight: Radius.circular(20.0),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12.0),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: const Icon(
                        Icons.keyboard_return,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.returnInvoice,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '#${widget.invoice.id.length > 13 ? widget.invoice.id.substring(0, 13) : widget.invoice.id}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.selectItemsToReturn,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Items List
                      ...widget.invoice.items.map(
                          (item) => _buildReturnItemCard(item, localizations)),

                      const SizedBox(height: 24),

                      // Return Summary
                      if (hasValidReturns) ...[
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.warningOrange.withValues(alpha: 0.1),
                                AppColors.warningOrange.withValues(alpha: 0.05),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.warningOrange
                                  .withValues(alpha: 0.3),
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${localizations.totalReturned}:',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primaryBlue,
                                    ),
                                  ),
                                  Text(
                                    'SAR ${totalReturnAmount.toStringAsFixed(2)}',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.warningOrange,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${localizations.netQuantity}:',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppColors.neutralGrey,
                                    ),
                                  ),
                                  Text(
                                    'SAR ${(widget.invoice.totalAmount - totalReturnAmount).toStringAsFixed(2)}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.successGreen,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              // Actions
              Container(
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.5),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(20.0),
                    bottomRight: Radius.circular(20.0),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        label: Text(localizations.cancel),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          side: const BorderSide(color: AppColors.primaryBlue),
                          foregroundColor: AppColors.primaryBlue,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: hasValidReturns ? _confirmReturn : null,
                        icon: const Icon(Icons.keyboard_return),
                        label: Text(localizations.confirmReturn),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: hasValidReturns
                              ? AppColors.warningOrange
                              : AppColors.neutralGrey,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReturnItemCard(
      InvoiceItem item, AppLocalizations localizations) {
    final availableToReturn = item.quantity - item.returnedQuantity;

    if (availableToReturn <= 0) {
      return const SizedBox.shrink(); // Don't show items that can't be returned
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product info
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.product.category,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                      Text(
                        'SAR ${item.product.pricePerUnit.toStringAsFixed(2)} / وحدة',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.neutralGrey,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.backgroundBlue,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${localizations.originalQuantity}: ${item.quantity}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ),
              ],
            ),

            if (item.returnedQuantity > 0) ...[
              const SizedBox(height: 8),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.warningOrange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${localizations.returnedQuantity}: ${item.returnedQuantity}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.warningOrange,
                  ),
                ),
              ),
            ],

            const SizedBox(height: 12),

            // Return quantity input
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.returnQuantity,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      TextFormField(
                        controller: controllers[item],
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        decoration: InputDecoration(
                          hintText: '0',
                          suffixText: '/ $availableToReturn',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        onChanged: (value) {
                          final qty = int.tryParse(value) ?? 0;
                          if (qty <= availableToReturn) {
                            setState(() {
                              returnQuantities[item] = qty;
                            });
                          } else {
                            controllers[item]!.text =
                                availableToReturn.toString();
                            setState(() {
                              returnQuantities[item] = availableToReturn;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () {
                    controllers[item]!.text = availableToReturn.toString();
                    setState(() {
                      returnQuantities[item] = availableToReturn;
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        AppColors.warningOrange.withValues(alpha: 0.1),
                    foregroundColor: AppColors.warningOrange,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: Text(localizations.fullReturn),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _confirmReturn() {
    final validReturns = <InvoiceItem, int>{};

    for (final entry in returnQuantities.entries) {
      if (entry.value > 0) {
        validReturns[entry.key] = entry.value;
      }
    }

    if (validReturns.isNotEmpty) {
      widget.onReturnConfirmed(validReturns);
      Navigator.pop(context);
    }
  }
}
