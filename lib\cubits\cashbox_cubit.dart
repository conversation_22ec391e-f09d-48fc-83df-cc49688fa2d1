import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import '../models/invoice.dart';
import '../models/product.dart';
import '../models/consumable.dart';

// Events
abstract class CashboxEvent extends Equatable {
  const CashboxEvent();

  @override
  List<Object> get props => [];
}

class LoadCashboxData extends CashboxEvent {}

// State
class CashboxState extends Equatable {
  final double totalSales;
  final double totalPurchases;
  final double totalProfit;
  final double totalConsumables;

  const CashboxState({
    this.totalSales = 0.0,
    this.totalPurchases = 0.0,
    this.totalProfit = 0.0,
    this.totalConsumables = 0.0,
  });

  CashboxState copyWith({
    double? totalSales,
    double? totalPurchases,
    double? totalProfit,
    double? totalConsumables,
  }) {
    return CashboxState(
      totalSales: totalSales ?? this.totalSales,
      totalPurchases: totalPurchases ?? this.totalPurchases,
      totalProfit: totalProfit ?? this.totalProfit,
      totalConsumables: totalConsumables ?? this.totalConsumables,
    );
  }

  @override
  List<Object> get props =>
      [totalSales, totalPurchases, totalProfit, totalConsumables];
}

// Cubit
class CashboxCubit extends Cubit<CashboxState> {
  final Box<Invoice> _invoicesBox;
  final Box<Product> _productsBox;
  final Box<Consumable> _consumablesBox;

  CashboxCubit(this._invoicesBox, this._productsBox, this._consumablesBox)
      : super(const CashboxState()) {
    loadCashboxData();
  }

  void loadCashboxData() {
    final invoices = _invoicesBox.values.toList();
    final products = _productsBox.values.toList();
    final consumables = _consumablesBox.values.toList();

    // Calculate total sales (totalAmount now already reflects returns)
    final totalSales = invoices.fold<double>(
      0.0,
      (sum, invoice) =>
          sum +
          invoice.totalAmount, // totalAmount now includes returns deduction
    );

    // Calculate total purchases = sum of (purchasePrice * quantity) for all products
    final totalPurchases = products.fold<double>(
      0.0,
      (sum, product) {
        try {
          // Use purchase price multiplied by quantity
          final purchasePrice = product.purchasePrice;
          return sum + (purchasePrice * product.quantity);
        } catch (e) {
          // Fallback for products without purchase price (old data)
          return sum + (product.pricePerUnit * product.quantity * 0.7);
        }
      },
    );

    // Calculate total profit from actual sales = total sales revenue - cost of sold items (accounting for returns)
    double totalProfit = 0.0;
    for (final invoice in invoices) {
      for (final item in invoice.items) {
        try {
          // Calculate profit for each net sold item: (selling price - purchase price) * net quantity sold
          final purchasePrice = item.product.purchasePrice;
          final netQuantitySold =
              item.netQuantity; // quantity - returnedQuantity
          final itemProfit =
              (item.product.pricePerUnit - purchasePrice) * netQuantitySold;
          totalProfit += itemProfit;
        } catch (e) {
          // Fallback for products without purchase price (old data)
          final estimatedPurchasePrice = item.product.pricePerUnit * 0.7;
          final netQuantitySold =
              item.netQuantity; // quantity - returnedQuantity
          final itemProfit =
              (item.product.pricePerUnit - estimatedPurchasePrice) *
                  netQuantitySold;
          totalProfit += itemProfit;
        }
      }
    }

    // Calculate total consumables price
    final totalConsumables = consumables.fold<double>(
      0.0,
      (sum, consumable) => sum + consumable.price,
    );

    emit(CashboxState(
      totalSales: totalSales,
      totalPurchases: totalPurchases,
      totalProfit: totalProfit,
      totalConsumables: totalConsumables,
    ));
  }

  // Method to update cashbox data when an invoice is added or deleted
  void updateCashbox() {
    loadCashboxData();
  }
}
