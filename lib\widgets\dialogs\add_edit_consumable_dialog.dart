import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/consumable.dart';
import '../../l10n/app_localizations.dart';
import '../../constants/app_colors.dart';

void showAddEditConsumableDialog({
  required BuildContext context,
  Consumable? consumable,
  required Function(String name, double price) onSave,
}) {
  showDialog(
    context: context,
    builder: (context) => AddEditConsumableDialog(
      consumable: consumable,
      onSave: onSave,
    ),
  );
}

class AddEditConsumableDialog extends StatefulWidget {
  final Consumable? consumable;
  final Function(String name, double price) onSave;

  const AddEditConsumableDialog({
    super.key,
    this.consumable,
    required this.onSave,
  });

  @override
  State<AddEditConsumableDialog> createState() =>
      _AddEditConsumableDialogState();
}

class _AddEditConsumableDialogState extends State<AddEditConsumableDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _priceController;

  @override
  void initState() {
    super.initState();
    _nameController =
        TextEditingController(text: widget.consumable?.name ?? '');
    _priceController = TextEditingController(
      text: widget.consumable?.price.toString() ?? '',
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  bool get isEditing => widget.consumable != null;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: const EdgeInsets.all(24.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.0),
          color: AppColors.surface,
        ),
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12.0),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Icon(
                        isEditing ? Icons.edit : Icons.add,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        isEditing
                            ? 'Edit Consumable'
                            : localizations.addConsumable,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Name Field
                _buildTextField(
                  controller: _nameController,
                  label: localizations.consumableName,
                  icon: Icons.label_outline,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations.pleaseEnterConsumableName;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Price Field
                _buildTextField(
                  controller: _priceController,
                  label: localizations.consumablePrice,
                  icon: Icons.attach_money_outlined,
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations.pleaseEnterConsumablePrice;
                    }
                    final number = double.tryParse(value);
                    if (number == null || number < 0) {
                      return localizations.pleaseEnterValidConsumablePrice;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 32),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          side: BorderSide(color: AppColors.primary),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                        ),
                        child: Text(
                          localizations.cancel,
                          style: TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          if (_formKey.currentState != null &&
                              _formKey.currentState!.validate()) {
                            final name = _nameController.text.trim();
                            final price = double.parse(_priceController.text);

                            widget.onSave(name, price);
                            Navigator.pop(context);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                        ),
                        child: Text(
                          isEditing ? 'Update' : localizations.add,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 16.0,
        ),
      ),
      keyboardType: keyboardType,
      inputFormatters: keyboardType == TextInputType.number
          ? [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))]
          : null,
      validator: validator,
    );
  }
}
