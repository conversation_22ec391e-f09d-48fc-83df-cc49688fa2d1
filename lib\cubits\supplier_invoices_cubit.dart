import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import '../models/supplier_invoice.dart';
import '../models/supplier.dart';

// State
class SupplierInvoicesState extends Equatable {
  final List<SupplierInvoice> supplierInvoices;
  final Map<String, List<SupplierInvoice>> supplierInvoicesMap;

  const SupplierInvoicesState({
    this.supplierInvoices = const [],
    this.supplierInvoicesMap = const {},
  });

  SupplierInvoicesState copyWith({
    List<SupplierInvoice>? supplierInvoices,
    Map<String, List<SupplierInvoice>>? supplierInvoicesMap,
  }) {
    return SupplierInvoicesState(
      supplierInvoices: supplierInvoices ?? this.supplierInvoices,
      supplierInvoicesMap: supplierInvoicesMap ?? this.supplierInvoicesMap,
    );
  }

  @override
  List<Object> get props => [supplierInvoices, supplierInvoicesMap];
}

// Cubit
class SupplierInvoicesCubit extends Cubit<SupplierInvoicesState> {
  final Box<SupplierInvoice> _supplierInvoicesBox;
  dynamic _suppliersCubit; // Using dynamic to avoid circular dependency
  dynamic _productsCubit; // Reference to products cubit
  dynamic _cashboxCubit; // Reference to cashbox cubit

  SupplierInvoicesCubit(this._supplierInvoicesBox)
      : super(const SupplierInvoicesState()) {
    loadSupplierInvoices();
  }

  // Set the suppliers cubit reference
  void setSuppliersCubit(dynamic suppliersCubit) {
    _suppliersCubit = suppliersCubit;
  }

  // Set the products cubit reference
  void setProductsCubit(dynamic productsCubit) {
    _productsCubit = productsCubit;
  }

  // Set the cashbox cubit reference
  void setCashboxCubit(dynamic cashboxCubit) {
    _cashboxCubit = cashboxCubit;
  }

  void loadSupplierInvoices() {
    final supplierInvoices = _supplierInvoicesBox.values.toList();
    final supplierInvoicesMap = <String, List<SupplierInvoice>>{};

    for (final supplierInvoice in supplierInvoices) {
      supplierInvoicesMap
          .putIfAbsent(supplierInvoice.supplier.name, () => [])
          .add(supplierInvoice);
    }

    emit(SupplierInvoicesState(
      supplierInvoices: supplierInvoices,
      supplierInvoicesMap: supplierInvoicesMap,
    ));
  }

  Future<void> addSupplierInvoice(SupplierInvoice supplierInvoice) async {
    // Update product quantities (add to inventory)
    for (final item in supplierInvoice.items) {
      final product = item.product;
      product.quantity += item.quantity;
      // Update purchase price if provided
      if (item.unitCost > 0) {
        product.purchasePrice = item.unitCost;
      }
      await product.save();
    }

    await _supplierInvoicesBox.add(supplierInvoice);
    loadSupplierInvoices();

    // Update cashbox data
    _cashboxCubit?.updateCashbox();

    // Update products data
    _productsCubit?.updateProducts();

    // Update supplier total paid
    _suppliersCubit?.updateSupplierTotalPaid();
  }

  Future<void> updateSupplierInvoice(SupplierInvoice supplierInvoice) async {
    await supplierInvoice.save();
    loadSupplierInvoices();

    // Update related cubits
    _cashboxCubit?.updateCashbox();
    _suppliersCubit?.updateSupplierTotalPaid();
  }

  Future<void> deleteSupplierInvoice(SupplierInvoice supplierInvoice) async {
    try {
      // Restore product quantities (remove from inventory) BEFORE deleting the invoice
      for (final item in supplierInvoice.items) {
        final product = item.product;
        // Check if product is still in a box before modifying
        if (product.isInBox) {
          product.quantity -= item.quantity;
          // Ensure quantity doesn't go negative
          if (product.quantity < 0) {
            product.quantity = 0;
          }
          await product.save();
        }
      }

      // Delete the supplier invoice
      await supplierInvoice.delete();
      loadSupplierInvoices();

      // Update related cubits
      _cashboxCubit?.updateCashbox();
      _productsCubit?.updateProducts();
      _suppliersCubit?.updateSupplierTotalPaid();
    } catch (e) {
      // If there's an error, still try to reload data
      loadSupplierInvoices();
      _cashboxCubit?.updateCashbox();
      _productsCubit?.updateProducts();
      _suppliersCubit?.updateSupplierTotalPaid();
      rethrow;
    }
  }

  // Process a payment from a supplier
  Future<void> processSupplierPayment(Supplier supplier, double amount) async {
    try {
      // Find unpaid supplier invoices for this supplier
      final supplierInvoices = _supplierInvoicesBox.values
          .where((supplierInvoice) =>
              supplierInvoice.supplier.name == supplier.name &&
              (!supplierInvoice.isPaid ||
                  (supplierInvoice.paidAmount ?? 0) <
                      supplierInvoice.totalAmount))
          .toList();

      // Sort by date (oldest first)
      supplierInvoices.sort((a, b) => a.date.compareTo(b.date));

      double remainingAmount = amount;

      // Apply payment to supplier invoices
      for (final supplierInvoice in supplierInvoices) {
        if (remainingAmount <= 0) break;

        final unpaidAmount =
            supplierInvoice.totalAmount - (supplierInvoice.paidAmount ?? 0);

        if (unpaidAmount > 0) {
          final paymentForThisInvoice =
              remainingAmount > unpaidAmount ? unpaidAmount : remainingAmount;

          // Update supplier invoice
          supplierInvoice.paidAmount =
              (supplierInvoice.paidAmount ?? 0) + paymentForThisInvoice;
          supplierInvoice.isPaid =
              supplierInvoice.paidAmount! >= supplierInvoice.totalAmount;
          await supplierInvoice.save();

          // Reduce remaining amount
          remainingAmount -= paymentForThisInvoice;
        }
      }

      // Reload data to update UI
      loadSupplierInvoices();

      // Update related cubits
      _cashboxCubit?.updateCashbox();
      _suppliersCubit?.updateSupplierTotalPaid();
    } catch (e) {
      // Error handling
      rethrow;
    }
  }

  // Get supplier invoices for a specific supplier
  List<SupplierInvoice> getSupplierInvoicesForSupplier(String supplierName) {
    return state.supplierInvoicesMap[supplierName] ?? [];
  }

  // Get total amount owed to suppliers
  double getTotalOwedToSuppliers() {
    return state.supplierInvoices.fold<double>(
      0.0,
      (sum, supplierInvoice) => sum + supplierInvoice.remainingAmount,
    );
  }

  // Get total paid to suppliers
  double getTotalPaidToSuppliers() {
    return state.supplierInvoices.fold<double>(
      0.0,
      (sum, supplierInvoice) => sum + (supplierInvoice.paidAmount ?? 0.0),
    );
  }

  // Generate the next invoice number automatically
  String generateNextInvoiceNumber() {
    final existingInvoices = state.supplierInvoices;

    // Find the highest existing invoice number
    int maxNumber = 0;
    for (final invoice in existingInvoices) {
      if (invoice.invoiceNumber != null) {
        // Try to parse the invoice number as an integer
        final number = int.tryParse(invoice.invoiceNumber!);
        if (number != null && number > maxNumber) {
          maxNumber = number;
        }
      }
    }

    // Return the next number
    return (maxNumber + 1).toString();
  }
}
