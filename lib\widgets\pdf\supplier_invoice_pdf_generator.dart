import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import '../../models/supplier_invoice.dart';
import '../../l10n/app_localizations.dart';
import '../invoices/pdf_invoice_components_2.dart' as components2;

class SupplierInvoicePdfGenerator {
  static Future<void> generateAndShareInvoice(
    BuildContext context,
    SupplierInvoice supplierInvoice,
  ) async {
    final localizations = AppLocalizations.of(context);
    final pdf = pw.Document();

    // Load the Arabic font
    final arabicFont =
        await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // Load the logo image
    final logoImage = await rootBundle.load('assets/images/logosplash.png');
    final logoImageData = logoImage.buffer.asUint8List();
    final logo = pw.MemoryImage(logoImageData);

    // Load the QR code image (or create QR code)
    final qrData =
        'فاتورة مورد رقم: ${supplierInvoice.invoiceNumber ?? supplierInvoice.id}';
    final qrCode = pw.BarcodeWidget(
      barcode: pw.Barcode.qrCode(),
      data: qrData,
      width: 60,
      height: 60,
    );

    // Create a theme with the Arabic font
    final theme = pw.ThemeData.withFont(
      base: ttf,
      bold: ttf,
      italic: ttf,
      boldItalic: ttf,
    );

    // Add a MultiPage to handle pagination automatically
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl, // Set RTL for Arabic text
        theme: theme, // Apply the theme with Arabic font
        // Set margin to ensure content doesn't get cut off
        margin:
            const pw.EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 10),
        // Set maxPages to a high number to ensure all content is included
        maxPages: 100,
        // Add page number footer
        footer: (context) => context.pageNumber == context.pagesCount
            ? components2.constFooter(ttf)
            : buildPageFooter(context, ttf),

        build: (context) {
          // Create a list of widgets to display
          final widgets = <pw.Widget>[];

          // Add header only once at the beginning
          widgets.add(
              _buildSupplierInvoiceHeader(ttf, logo, qrCode, localizations));
          widgets.add(pw.SizedBox(height: 10));

          // Add invoice details
          widgets.add(
              _buildSupplierInvoiceDetails(supplierInvoice, ttf, logo, qrCode));
          widgets.add(pw.SizedBox(height: 15));

          // Add the items table with header on each page
          // The table will automatically flow to the next page when needed
          widgets.add(
            pw.Table(
              columnWidths: {
                0: const pw.FlexColumnWidth(0.5), // #
                1: const pw.FlexColumnWidth(3), // Product
                2: const pw.FlexColumnWidth(1.5), // Quantity/Unit
                3: const pw.FlexColumnWidth(2), // Unit Cost
                4: const pw.FlexColumnWidth(2), // Total Cost
              },
              border: pw.TableBorder.all(
                color: PdfColor.fromHex('#4C585B'), // Dark gray border
                width: 1,
              ),
              tableWidth: pw.TableWidth.max,
              defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
              children: [
                // Table header - always show on each page
                _buildSupplierItemsTableHeader(ttf),

                // Table rows - show all rows and let MultiPage handle pagination
                if (supplierInvoice.items.isNotEmpty)
                  ...supplierInvoice.items.asMap().entries.map(
                        (entry) => _buildSupplierItemsTableRow(
                            entry.key, entry.value, ttf),
                      )
                else
                  // Show an empty row if there are no items
                  pw.TableRow(
                    children: List.generate(
                      5,
                      (index) => pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          index == 1
                              ? 'لا توجد منتجات'
                              : '', // "No products" in the product column
                          style: pw.TextStyle(
                            font: ttf,
                            fontSize: 9,
                            color: PdfColor.fromHex('#4C585B'),
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );

          // Always add the summary section at the end of the content
          widgets.add(pw.SizedBox(height: 10));

          // Use supplierInvoice.totalAmount for summary
          widgets.add(_buildSupplierSummarySection(supplierInvoice.totalAmount,
              supplierInvoice.paidAmount ?? 0.0, ttf,
              collectedAmount: supplierInvoice.collectedAmount));

          return widgets;
        },
      ),
    );

    final output = await getTemporaryDirectory();
    final invoiceDisplayNumber =
        supplierInvoice.invoiceNumber ?? supplierInvoice.id;
    final file =
        File('${output.path}/supplier_invoice_$invoiceDisplayNumber.pdf');
    await file.writeAsBytes(await pdf.save());

    await Share.shareXFiles(
      [XFile(file.path)],
      text: 'فاتورة مورد #$invoiceDisplayNumber',
    );
  }

  // Page footer with page number
  static pw.Widget buildPageFooter(pw.Context context, pw.Font ttf) {
    return pw.Container(
      alignment: pw.Alignment.center,
      margin: const pw.EdgeInsets.only(top: 10),
      child: pw.Text(
        'صفحة ${context.pageNumber} من ${context.pagesCount}',
        style: pw.TextStyle(
          font: ttf,
          fontSize: 10,
          color: PdfColor.fromHex('#666666'),
        ),
      ),
    );
  }

  static pw.Widget _buildSupplierInvoiceHeader(
    pw.Font ttf,
    pw.MemoryImage logo,
    pw.Widget qrCode,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(10),
        border: pw.Border.all(color: PdfColors.blue200, width: 1),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'فاتورة مورد',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'مؤسسة محمد علي بكري الزبيدي البيطرية',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 14,
                    color: PdfColors.blue600,
                  ),
                ),
              ],
            ),
          ),
          pw.Container(
            width: 60,
            height: 60,
            child: pw.Image(logo),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSupplierInvoiceDetails(
    SupplierInvoice supplierInvoice,
    pw.Font ttf,
    pw.MemoryImage logo,
    pw.Widget qrCode,
  ) {
    final invoiceDisplayNumber = supplierInvoice.invoiceNumber ??
        (supplierInvoice.id.length > 8
            ? supplierInvoice.id.substring(0, 8)
            : supplierInvoice.id);

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400, width: 1),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'رقم الفاتورة: $invoiceDisplayNumber',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.black,
                      ),
                    ),
                    pw.SizedBox(height: 5),
                    pw.Text(
                      'اسم المورد: ${supplierInvoice.supplier.name}',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 14,
                        color: PdfColors.grey700,
                      ),
                    ),
                    pw.Text(
                      'رقم الهاتف: ${supplierInvoice.supplier.phoneNumber}',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 12,
                        color: PdfColors.grey700,
                      ),
                    ),
                    if (supplierInvoice.supplier.address != null &&
                        supplierInvoice.supplier.address!.isNotEmpty)
                      pw.Text(
                        'العنوان: ${supplierInvoice.supplier.address}',
                        style: pw.TextStyle(
                          font: ttf,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                    pw.Text(
                      'التاريخ: ${_formatDate(supplierInvoice.date)}',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 12,
                        color: PdfColors.grey700,
                      ),
                    ),
                  ],
                ),
              ),
              pw.Container(
                width: 60,
                height: 60,
                child: qrCode,
              ),
            ],
          ),
        ],
      ),
    );
  }

  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  static pw.TableRow _buildSupplierItemsTableHeader(pw.Font ttf) {
    return pw.TableRow(
      decoration: const pw.BoxDecoration(color: PdfColors.grey200),
      children: [
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            '#',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            'المنتج',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            'الكمية',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            'سعر الوحدة',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            'الإجمالي',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
      ],
    );
  }

  static pw.TableRow _buildSupplierItemsTableRow(
    int index,
    SupplierInvoiceItem item,
    pw.Font ttf,
  ) {
    return pw.TableRow(
      children: [
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            '${index + 1}',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 9,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                item.product.category,
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 9,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColor.fromHex('#4C585B'),
                ),
              ),
              pw.Text(
                'سعر البيع: ${item.product.pricePerUnit.toStringAsFixed(2)} ريال',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 8,
                  color: PdfColor.fromHex('#666666'),
                ),
              ),
            ],
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            '${item.quantity}',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 9,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            '${item.unitCost.toStringAsFixed(2)} ريال',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 9,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            '${item.totalPrice.toStringAsFixed(2)} ريال',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 9,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('#4C585B'),
            ),
            textAlign: pw.TextAlign.center,
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildSupplierSummarySection(
    double totalAmount,
    double paidAmount,
    pw.Font ttf, {
    double collectedAmount = 0.0,
  }) {
    final remainingAmount = totalAmount - paidAmount;

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200, width: 1),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص الفاتورة',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'المبلغ الإجمالي:',
                style: pw.TextStyle(font: ttf, fontSize: 12),
              ),
              pw.Text(
                '${totalAmount.toStringAsFixed(2)} ريال',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'المبلغ المدفوع:',
                style: pw.TextStyle(font: ttf, fontSize: 12),
              ),
              pw.Text(
                '${paidAmount.toStringAsFixed(2)} ريال',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ],
          ),
          if (collectedAmount > 0) ...[
            pw.SizedBox(height: 5),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  'المبلغ المحصل:',
                  style: pw.TextStyle(font: ttf, fontSize: 12),
                ),
                pw.Text(
                  '${collectedAmount.toStringAsFixed(2)} ريال',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
          pw.SizedBox(height: 10),
          pw.Container(
            width: double.infinity,
            height: 1,
            color: PdfColors.blue300,
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'المبلغ المتبقي:',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.Text(
                '${remainingAmount.toStringAsFixed(2)} ريال',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: remainingAmount > 0 ? PdfColors.red : PdfColors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
