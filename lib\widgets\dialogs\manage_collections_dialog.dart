import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/consumable.dart';
import '../../models/consumable_collection.dart';
import '../../cubits/consumable_collections_cubit.dart';
import '../../cubits/consumables_cubit.dart';
import '../../l10n/app_localizations.dart';
import '../../constants/app_colors.dart';
import '../../pages/collection_detail_page.dart';

void showManageCollectionsDialog({
  required BuildContext context,
  required Function(List<Consumable>, String, String) onCollectionLoaded,
}) {
  showDialog(
    context: context,
    builder: (context) => ManageCollectionsDialog(
      onCollectionLoaded: onCollectionLoaded,
    ),
  );
}

class ManageCollectionsDialog extends StatefulWidget {
  final Function(List<Consumable>, String, String) onCollectionLoaded;

  const ManageCollectionsDialog({
    super.key,
    required this.onCollectionLoaded,
  });

  @override
  State<ManageCollectionsDialog> createState() =>
      _ManageCollectionsDialogState();
}

class _ManageCollectionsDialogState extends State<ManageCollectionsDialog> {
  final _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _openCollectionDetail(ConsumableCollection collection) {
    Navigator.of(context).pop(); // Close the dialog first
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CollectionDetailPage(collection: collection),
      ),
    );
  }

  void _deleteCollection(ConsumableCollection collection) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).deleteCollection),
        content: Text(AppLocalizations.of(context).confirmDeleteCollection),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final collectionsCubit =
                    context.read<ConsumableCollectionsCubit>();
                await collectionsCubit.deleteCollection(collection.id);

                if (mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${collection.name} deleted'),
                      backgroundColor: Colors.orange,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context).delete),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.collections_bookmark,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    localizations.manageCollections,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C2C2C),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Search bar
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search collections...',
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.primary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: AppColors.primary,
                    width: 2,
                  ),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),

            const SizedBox(height: 16),

            // Collections list
            Expanded(
              child: BlocBuilder<ConsumableCollectionsCubit,
                  List<ConsumableCollection>>(
                builder: (context, collections) {
                  // Filter collections based on search query
                  final filteredCollections = _searchQuery.isEmpty
                      ? collections
                      : collections
                          .where((collection) => collection.name
                              .toLowerCase()
                              .contains(_searchQuery.toLowerCase()))
                          .toList();

                  if (filteredCollections.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _searchQuery.isEmpty
                                ? Icons.bookmark_border
                                : Icons.search_off,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isEmpty
                                ? localizations.noCollections
                                : 'No collections found',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return BlocBuilder<ConsumablesCubit, ConsumablesState>(
                    builder: (context, consumablesState) {
                      return ListView.builder(
                        itemCount: filteredCollections.length,
                        itemBuilder: (context, index) {
                          final collection = filteredCollections[index];
                          final isCurrentCollection =
                              consumablesState.currentCollectionId ==
                                  collection.id;

                          return Card(
                            margin: const EdgeInsets.only(bottom: 12),
                            elevation: isCurrentCollection ? 4 : 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: isCurrentCollection
                                  ? BorderSide(
                                      color: AppColors.primary, width: 2)
                                  : BorderSide.none,
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: isCurrentCollection
                                      ? [
                                          AppColors.primary
                                              .withValues(alpha: 0.1),
                                          AppColors.surface
                                        ]
                                      : [
                                          AppColors.surface,
                                          AppColors.surfaceVariant
                                        ],
                                ),
                              ),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                onTap: () => _openCollectionDetail(collection),
                                leading: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: isCurrentCollection
                                        ? AppColors.primary
                                        : AppColors.primary
                                            .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    isCurrentCollection
                                        ? Icons.bookmark
                                        : Icons.bookmark_border,
                                    color: isCurrentCollection
                                        ? Colors.white
                                        : AppColors.primary,
                                    size: 20,
                                  ),
                                ),
                                title: Text(
                                  collection.name,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 4),
                                    Text(
                                      '${collection.itemCount} items • SAR ${collection.totalPrice.toStringAsFixed(2)}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppColors.primary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    Text(
                                      'Updated: ${_formatDate(collection.updatedAt)}',
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                                trailing: IconButton(
                                  onPressed: () =>
                                      _deleteCollection(collection),
                                  icon: Icon(
                                    Icons.delete,
                                    color: AppColors.error,
                                  ),
                                  tooltip: localizations.deleteCollection,
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
