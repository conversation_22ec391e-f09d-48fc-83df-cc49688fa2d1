import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/consumable.dart';
import '../models/consumable_collection.dart';
import '../cubits/consumable_collections_cubit.dart';
import '../cubits/consumables_cubit.dart';
import '../l10n/app_localizations.dart';
import '../constants/app_colors.dart';
import '../widgets/dialogs/add_edit_consumable_dialog.dart';

class CollectionDetailPage extends StatefulWidget {
  final ConsumableCollection collection;

  const CollectionDetailPage({
    super.key,
    required this.collection,
  });

  @override
  State<CollectionDetailPage> createState() => _CollectionDetailPageState();
}

class _CollectionDetailPageState extends State<CollectionDetailPage> {
  late List<Consumable> _consumables;
  late String _collectionName;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _collectionName = widget.collection.name;
    _loadCollectionData();
  }

  void _loadCollectionData() {
    try {
      final collectionsCubit = context.read<ConsumableCollectionsCubit>();
      _consumables = collectionsCubit.loadCollection(widget.collection.id);
    } catch (e) {
      _consumables = [];
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading collection: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _addConsumable() {
    showAddEditConsumableDialog(
      context: context,
      onSave: (name, price) {
        setState(() {
          _consumables.add(Consumable(
            name: name,
            price: price,
          ));
          _hasChanges = true;
        });
      },
    );
  }

  void _editConsumable(int index) {
    final consumable = _consumables[index];
    showAddEditConsumableDialog(
      context: context,
      consumable: consumable,
      onSave: (name, price) {
        setState(() {
          _consumables[index] = Consumable(
            name: name,
            price: price,
          );
          _hasChanges = true;
        });
      },
    );
  }

  void _deleteConsumable(int index) {
    final consumable = _consumables[index];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).deleteConsumable),
        content: Text('Are you sure you want to delete "${consumable.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _consumables.removeAt(index);
                _hasChanges = true;
              });
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context).delete),
          ),
        ],
      ),
    );
  }

  void _saveChanges() async {
    try {
      final collectionsCubit = context.read<ConsumableCollectionsCubit>();
      await collectionsCubit.updateCollection(
        widget.collection.id,
        _collectionName,
        _consumables,
      );

      setState(() {
        _hasChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Collection saved successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving collection: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _editCollectionName() {
    final controller = TextEditingController(text: _collectionName);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Collection Name'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: 'Collection Name',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                setState(() {
                  _collectionName = controller.text.trim();
                  _hasChanges = true;
                });
                Navigator.of(context).pop();
              }
            },
            child: Text('Save'),
          ),
        ],
      ),
    );
  }

  double get _totalPrice {
    return _consumables.fold(0.0, (sum, consumable) => sum + consumable.price);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: GestureDetector(
          onTap: _editCollectionName,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  _collectionName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Icon(Icons.edit, size: 16),
            ],
          ),
        ),
        actions: [
          if (_hasChanges)
            IconButton(
              onPressed: _saveChanges,
              icon: const Icon(Icons.save),
              tooltip: 'Save Changes',
            ),
        ],
      ),
      body: Column(
        children: [
          // Collection Summary
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.collections_bookmark,
                        color: AppColors.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Collection Summary',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${_consumables.length} items • SAR ${_totalPrice.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Consumables List
          Expanded(
            child: _consumables.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: AppColors.textHint,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No consumables in this collection',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Tap the + button to add consumables',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textHint,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _consumables.length,
                    itemBuilder: (context, index) {
                      final consumable = _consumables[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ListTile(
                          contentPadding: const EdgeInsets.all(16),
                          leading: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppColors.accent.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.inventory_2,
                              color: AppColors.accent,
                              size: 20,
                            ),
                          ),
                          title: Text(
                            consumable.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          subtitle: Text(
                            'SAR ${consumable.price.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                onPressed: () => _editConsumable(index),
                                icon: Icon(
                                  Icons.edit,
                                  color: AppColors.primary,
                                ),
                                tooltip: 'Edit',
                              ),
                              IconButton(
                                onPressed: () => _deleteConsumable(index),
                                icon: Icon(
                                  Icons.delete,
                                  color: AppColors.error,
                                ),
                                tooltip: 'Delete',
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addConsumable,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }
}
