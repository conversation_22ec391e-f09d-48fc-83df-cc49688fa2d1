import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/supplier_invoice.dart';
import '../models/supplier.dart';
import '../cubits/supplier_payment_records_cubit.dart';
import '../cubits/suppliers_cubit.dart';
import '../l10n/app_localizations.dart';
import '../widgets/pdf/supplier_statement_generator.dart';
import '../constants/app_colors.dart';

class SupplierSummaryPage extends StatelessWidget {
  final Supplier supplier;
  final List<SupplierInvoice> invoices;

  const SupplierSummaryPage({
    super.key,
    required this.supplier,
    required this.invoices,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final supplierPaymentRecordsCubit = context.read<SupplierPaymentRecordsCubit>();

    // Calculate summary statistics
    final totalInvoices = invoices.length;
    final totalInvoiceAmount = invoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );

    // Get total paid amount from payment records
    final totalPaidFromPayments =
        supplierPaymentRecordsCubit.getTotalPaymentAmountForSupplier(supplier.name);

    // Get total collected amount from invoices
    final totalCollectedAmount =
        supplierPaymentRecordsCubit.getTotalCollectedAmountForSupplier(supplier.name);

    final remainingBalance = totalInvoiceAmount - totalPaidFromPayments;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'ملخص المورد - ${supplier.name}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryBlue,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _shareSupplierStatement(context, localizations),
            icon: const Icon(
              Icons.share,
              color: Colors.white,
            ),
            tooltip: 'مشاركة كشف الحساب',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Supplier Info Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.backgroundBlue,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.business,
                          color: AppColors.primaryBlue,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              supplier.name,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryBlue,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'رقم الهاتف: ${supplier.phoneNumber}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.neutralGrey,
                              ),
                            ),
                            if (supplier.address != null && supplier.address!.isNotEmpty)
                              Text(
                                'العنوان: ${supplier.address}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.neutralGrey,
                                ),
                              ),
                            if (supplier.email != null && supplier.email!.isNotEmpty)
                              Text(
                                'البريد الإلكتروني: ${supplier.email}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.neutralGrey,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Financial Summary Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الملخص المالي',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  _buildSummaryRow('عدد الفواتير', '$totalInvoices فاتورة', Icons.receipt_long),
                  const SizedBox(height: 12),
                  _buildSummaryRow('إجمالي قيمة الفواتير', '${totalInvoiceAmount.toStringAsFixed(2)} ريال', Icons.monetization_on),
                  const SizedBox(height: 12),
                  _buildSummaryRow('إجمالي المدفوع', '${totalPaidFromPayments.toStringAsFixed(2)} ريال', Icons.payment),
                  const SizedBox(height: 12),
                  _buildSummaryRow('إجمالي المبلغ المحصل', '${totalCollectedAmount.toStringAsFixed(2)} ريال', Icons.account_balance_wallet),
                  const SizedBox(height: 12),
                  _buildSummaryRow(
                    'المبلغ المتبقي',
                    '${remainingBalance.toStringAsFixed(2)} ريال',
                    Icons.account_balance,
                    valueColor: remainingBalance > 0 ? AppColors.warningOrange : AppColors.successGreen,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Invoice Details
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations.invoices,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Invoice list
                  ...invoices.map((invoice) {
                    return _buildInvoiceCard(context, invoice, localizations);
                  }).toList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, IconData icon, {Color? valueColor}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.primaryBlue,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.neutralGrey,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: valueColor ?? AppColors.darkGrey,
          ),
        ),
      ],
    );
  }

  Widget _buildInvoiceCard(
    BuildContext context,
    SupplierInvoice invoice,
    AppLocalizations localizations,
  ) {
    final supplierPaymentRecordsCubit = context.read<SupplierPaymentRecordsCubit>();
    
    // Get paid amount from payment records
    final paidAmount =
        supplierPaymentRecordsCubit.getTotalPaymentAmountForSupplierInvoice(invoice.id);
    final remainingAmount = invoice.totalAmount - paidAmount;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 6),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Color(0xFFFAFAFA),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'فاتورة رقم ${invoice.invoiceNumber ?? invoice.id.substring(0, 8)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryBlue,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: remainingAmount <= 0 ? AppColors.successGreen : AppColors.warningOrange,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    remainingAmount <= 0 ? 'مدفوعة' : 'غير مدفوعة',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'التاريخ: ${_formatDate(invoice.date)}',
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.neutralGrey,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ الإجمالي: ${invoice.totalAmount.toStringAsFixed(2)} ريال',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.darkGrey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ المدفوع: ${paidAmount.toStringAsFixed(2)} ريال',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.successGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ المتبقي: ${remainingAmount.toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    fontSize: 14,
                    color: remainingAmount > 0 ? AppColors.warningOrange : AppColors.successGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            if (invoice.notes != null && invoice.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'ملاحظات: ${invoice.notes}',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.neutralGrey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _shareSupplierStatement(
      BuildContext context, AppLocalizations localizations) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Get payment records for this supplier
      final supplierPaymentRecordsCubit = context.read<SupplierPaymentRecordsCubit>();
      final paymentRecords =
          supplierPaymentRecordsCubit.getPaymentRecordsForSupplier(supplier.name);

      // Generate and share the PDF
      await SupplierStatementGenerator.generateAndShareStatement(
        context,
        supplier,
        invoices,
        paymentRecords,
      );

      // Close loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Close loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
        
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء كشف الحساب: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }
}
