import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import '../models/supplier.dart';
import '../models/supplier_invoice.dart';

// State
class SuppliersState extends Equatable {
  final List<Supplier> suppliers;

  const SuppliersState({
    this.suppliers = const [],
  });

  SuppliersState copyWith({
    List<Supplier>? suppliers,
  }) {
    return SuppliersState(
      suppliers: suppliers ?? this.suppliers,
    );
  }

  @override
  List<Object> get props => [suppliers];
}

// Cubit
class SuppliersCubit extends Cubit<SuppliersState> {
  final Box<Supplier> _suppliersBox;
  Box<SupplierInvoice>? _supplierInvoicesBox;
  dynamic _supplierInvoicesCubit; // Using dynamic to avoid circular dependency

  SuppliersCubit(this._suppliersBox) : super(const SuppliersState()) {
    loadSuppliers();
  }

  // Set the supplier invoices box reference to calculate total paid
  void setSupplierInvoicesBox(Box<SupplierInvoice> supplierInvoicesBox) {
    _supplierInvoicesBox = supplierInvoicesBox;
    updateSupplierTotalPaid();
  }

  // Set the supplier invoices cubit reference
  void setSupplierInvoicesCubit(dynamic supplierInvoicesCubit) {
    _supplierInvoicesCubit = supplierInvoicesCubit;
  }

  void loadSuppliers() {
    final suppliers = _suppliersBox.values.toList();
    suppliers.sort((a, b) => a.name.compareTo(b.name));
    emit(SuppliersState(suppliers: suppliers));
  }

  Future<void> addSupplier(Supplier supplier) async {
    await _suppliersBox.add(supplier);
    loadSuppliers();
  }

  Future<void> updateSupplier(Supplier supplier) async {
    await supplier.save();
    loadSuppliers();
  }

  Future<void> deleteSupplier(Supplier supplier) async {
    try {
      if (_supplierInvoicesBox != null && _supplierInvoicesCubit != null) {
        // Find all supplier invoices associated with this supplier
        final supplierInvoices = _supplierInvoicesBox!.values
            .where((invoice) => invoice.supplier.name == supplier.name)
            .toList();

        // Delete each supplier invoice
        for (final invoice in supplierInvoices) {
          try {
            await _supplierInvoicesCubit.deleteSupplierInvoice(invoice);
          } catch (e) {
            // Continue with other invoices even if one fails
            continue;
          }
        }
      }

      // Delete the supplier
      if (supplier.isInBox) {
        await supplier.delete();
      }
      loadSuppliers();
    } catch (e) {
      // If there's an error, still try to reload data
      loadSuppliers();
      rethrow;
    }
  }

  // Update supplier total paid and remaining balance based on supplier invoices
  void updateSupplierTotalPaid() {
    if (_supplierInvoicesBox == null) return;

    final suppliers = _suppliersBox.values.toList();
    final supplierInvoices = _supplierInvoicesBox!.values.toList();

    for (final supplier in suppliers) {
      final supplierInvoicesList = supplierInvoices
          .where((invoice) => invoice.supplier.name == supplier.name)
          .toList();

      // Calculate total paid by this supplier
      final totalPaid = supplierInvoicesList.fold<double>(
        0.0,
        (sum, invoice) => sum + (invoice.paidAmount ?? 0.0),
      );

      // Calculate total amount owed to this supplier
      final totalOwed = supplierInvoicesList.fold<double>(
        0.0,
        (sum, invoice) => sum + invoice.totalAmount,
      );

      // Calculate remaining balance (what we still owe the supplier)
      final remainingBalance = totalOwed - totalPaid;

      // Update supplier data
      supplier.totalPaid = totalPaid;
      supplier.remainingBalance = remainingBalance;
      supplier.save();
    }

    loadSuppliers();
  }

  // Process a payment to a supplier
  Future<void> processSupplierPayment(Supplier supplier, double amount) async {
    try {
      // Find unpaid supplier invoices for this supplier
      final supplierInvoices = _supplierInvoicesBox?.values
              .where((invoice) =>
                  invoice.supplier.name == supplier.name &&
                  (!invoice.isPaid ||
                      (invoice.paidAmount ?? 0) < invoice.totalAmount))
              .toList() ??
          [];

      // Sort by date (oldest first)
      supplierInvoices.sort((a, b) => a.date.compareTo(b.date));

      double remainingAmount = amount;

      // Apply payment to supplier invoices
      for (final invoice in supplierInvoices) {
        if (remainingAmount <= 0) break;

        final unpaidAmount = invoice.totalAmount - (invoice.paidAmount ?? 0);

        if (unpaidAmount > 0) {
          final paymentForThisInvoice =
              remainingAmount > unpaidAmount ? unpaidAmount : remainingAmount;

          // Update supplier invoice
          invoice.paidAmount =
              (invoice.paidAmount ?? 0) + paymentForThisInvoice;
          invoice.isPaid = invoice.paidAmount! >= invoice.totalAmount;
          await invoice.save();

          // Reduce remaining amount
          remainingAmount -= paymentForThisInvoice;
        }
      }

      // Update supplier totals
      updateSupplierTotalPaid();

      // Update supplier invoices cubit if available
      if (_supplierInvoicesCubit != null) {
        _supplierInvoicesCubit.loadSupplierInvoices();
      }
    } catch (e) {
      // Handle errors
      rethrow;
    }
  }
}
