import 'package:alzobidi/widgets/invoices/pdf_invoice_components_2.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import '../../models/supplier_invoice.dart';
import '../../models/supplier.dart';
import '../../models/supplier_payment_record.dart';
import '../../l10n/app_localizations.dart';

// Transaction class to represent different types of supplier transactions
class SupplierTransaction {
  final DateTime date;
  final String description;
  final String referenceNumber;
  final double debitAmount; // له (amount we owe to supplier)
  final double creditAmount; // عليه (amount supplier owes us - payments made)
  final String type; // 'invoice', 'payment'

  SupplierTransaction({
    required this.date,
    required this.description,
    required this.referenceNumber,
    required this.debitAmount,
    required this.creditAmount,
    required this.type,
  });
}

class SupplierStatementGenerator {
  static Future<void> generateAndShareStatement(
    BuildContext context,
    Supplier supplier,
    List<SupplierInvoice> invoices,
    List<SupplierPaymentRecord> paymentRecords,
  ) async {
    final localizations = AppLocalizations.of(context);
    final pdf = pw.Document();

    // Load the Arabic font
    final arabicFont =
        await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // Load logo
    final logoBytes = await rootBundle.load('assets/images/logosplash.png');
    final logo = pw.MemoryImage(logoBytes.buffer.asUint8List());

    // Create QR code data
    final qrData = 'كشف حساب المورد: ${supplier.name}';
    final qrCode = pw.BarcodeWidget(
      barcode: pw.Barcode.qrCode(),
      data: qrData,
      width: 60,
      height: 60,
    );

    // Collect all transactions
    List<SupplierTransaction> transactions = [];

    // Add invoice transactions
    for (final invoice in invoices) {
      final invoiceDisplayNumber = invoice.invoiceNumber ??
          (invoice.id.length > 8 ? invoice.id.substring(0, 8) : invoice.id);

      transactions.add(SupplierTransaction(
        date: invoice.date,
        description: 'فاتورة مشتريات رقم $invoiceDisplayNumber',
        referenceNumber: invoiceDisplayNumber,
        debitAmount: invoice.totalAmount, // We owe the supplier
        creditAmount: 0.0,
        type: 'invoice',
      ));
    }

    // Add payment transactions
    for (final payment in paymentRecords) {
      if (payment.invoiceId == 'EXCESS_PAYMENT') {
        transactions.add(SupplierTransaction(
          date: payment.timestamp,
          description: 'دفعة إضافية',
          referenceNumber: '-',
          debitAmount: 0.0,
          creditAmount: payment.amount, // Payment made to supplier
          type: 'payment',
        ));
      } else {
        // Find the corresponding invoice to get the display number
        final correspondingInvoice = invoices.firstWhere(
          (invoice) => invoice.id == payment.invoiceId,
          orElse: () => SupplierInvoice(
            id: payment.invoiceId,
            supplier: supplier,
            items: [],
            date: payment.timestamp,
            totalAmount: 0.0,
          ),
        );
        final paymentInvoiceDisplayNumber =
            correspondingInvoice.invoiceNumber ??
                (payment.invoiceId.length > 8
                    ? payment.invoiceId.substring(0, 8)
                    : payment.invoiceId);

        transactions.add(SupplierTransaction(
          date: payment.timestamp,
          description: 'دفعة لفاتورة رقم $paymentInvoiceDisplayNumber',
          referenceNumber: paymentInvoiceDisplayNumber,
          debitAmount: 0.0,
          creditAmount: payment.amount, // Payment made to supplier
          type: 'payment',
        ));
      }
    }

    // Sort transactions by date (oldest first)
    transactions.sort((a, b) => a.date.compareTo(b.date));

    // Calculate running balance and create transactions with balance
    List<Map<String, dynamic>> transactionsWithBalance = [];
    double runningBalance = 0.0;

    for (final transaction in transactions) {
      runningBalance += transaction.debitAmount - transaction.creditAmount;
      transactionsWithBalance.add({
        'transaction': transaction,
        'balance': runningBalance,
      });
    }

    // Calculate totals
    final totalDebit =
        transactions.fold<double>(0.0, (sum, t) => sum + t.debitAmount);
    final totalCredit =
        transactions.fold<double>(0.0, (sum, t) => sum + t.creditAmount);
    final finalBalance = totalDebit - totalCredit;

    // Calculate date range
    DateTime? fromDate;
    DateTime? toDate;
    if (transactions.isNotEmpty) {
      fromDate = transactions.first.date;
      toDate = transactions.last.date;
    }

    // Create theme
    final theme = pw.ThemeData.withFont(
      base: ttf,
      bold: ttf,
      italic: ttf,
      boldItalic: ttf,
    );

    // Add page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: theme,
        margin:
            const pw.EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 10),
        maxPages: 100,
        build: (context) => [
          _buildNewStatementHeader(ttf, logo, localizations),
          pw.SizedBox(height: 20),
          _buildNewSupplierInfoHeader(
              supplier, ttf, localizations, logo, fromDate, toDate, qrCode),
          pw.SizedBox(height: 20),
          _buildNewTransactionsTable(
              transactionsWithBalance, ttf, localizations),
          pw.SizedBox(height: 20),
          _buildNewSummarySection(
              totalDebit, totalCredit, finalBalance, ttf, localizations),
          // Add space before footer to push it to bottom
          pw.SizedBox(height: 40),
          // Footer only appears on the last page
        ],
        footer: (context) => context.pageNumber == context.pagesCount
            ? constFooter(ttf)
            : buildPageFooter(context, ttf),
      ),
    );

    // Save and share the PDF
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/supplier_statement_${supplier.name}.pdf');
    await file.writeAsBytes(await pdf.save());

    await Share.shareXFiles(
      [XFile(file.path)],
      text: 'كشف حساب المورد - ${supplier.name}',
    );
  }

  static pw.Widget _buildNewStatementHeader(
    pw.Font ttf,
    pw.MemoryImage logo,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(10),
        border: pw.Border.all(color: PdfColors.blue200, width: 1),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'كشف حساب مورد',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'مؤسسة محمد علي بكري الزبيدي البيطرية',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 14,
                    color: PdfColors.blue600,
                  ),
                ),
              ],
            ),
          ),
          pw.Container(
            width: 60,
            height: 60,
            child: pw.Image(logo),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildNewSupplierInfoHeader(
    Supplier supplier,
    pw.Font ttf,
    AppLocalizations localizations,
    pw.MemoryImage logo,
    DateTime? fromDate,
    DateTime? toDate,
    pw.Widget qrCode,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400, width: 1),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'اسم المورد: ${supplier.name}',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.black,
                      ),
                    ),
                    pw.SizedBox(height: 5),
                    pw.Text(
                      'رقم الهاتف: ${supplier.phoneNumber}',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 12,
                        color: PdfColors.grey700,
                      ),
                    ),
                    if (supplier.address != null &&
                        supplier.address!.isNotEmpty)
                      pw.Text(
                        'العنوان: ${supplier.address}',
                        style: pw.TextStyle(
                          font: ttf,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                    if (supplier.email != null && supplier.email!.isNotEmpty)
                      pw.Text(
                        'البريد الإلكتروني: ${supplier.email}',
                        style: pw.TextStyle(
                          font: ttf,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                  ],
                ),
              ),
              pw.Container(
                width: 60,
                height: 60,
                child: qrCode,
              ),
            ],
          ),
          if (fromDate != null && toDate != null) ...[
            pw.SizedBox(height: 10),
            pw.Text(
              'فترة الكشف: من ${_formatDate(fromDate)} إلى ${_formatDate(toDate)}',
              style: pw.TextStyle(
                font: ttf,
                fontSize: 12,
                color: PdfColors.grey600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  static pw.Widget _buildNewTransactionsTable(
    List<Map<String, dynamic>> transactionsWithBalance,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    if (transactionsWithBalance.isEmpty) {
      return pw.Container(
        width: double.infinity,
        padding: const pw.EdgeInsets.all(20),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey400, width: 1),
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Center(
          child: pw.Text(
            'لا توجد معاملات لعرضها',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 14,
              color: PdfColors.grey600,
            ),
          ),
        ),
      );
    }

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400, width: 0.5),
      columnWidths: {
        0: const pw.FlexColumnWidth(2), // Date
        1: const pw.FlexColumnWidth(4), // Description
        2: const pw.FlexColumnWidth(2), // Reference
        3: const pw.FlexColumnWidth(2), // Debit (له)
        4: const pw.FlexColumnWidth(2), // Credit (عليه)
        5: const pw.FlexColumnWidth(2), // Balance
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('التاريخ', ttf, isHeader: true),
            _buildTableCell('البيان', ttf, isHeader: true),
            _buildTableCell('المرجع', ttf, isHeader: true),
            _buildTableCell('له', ttf, isHeader: true),
            _buildTableCell('عليه', ttf, isHeader: true),
            _buildTableCell('الرصيد', ttf, isHeader: true),
          ],
        ),
        // Data rows
        ...transactionsWithBalance.map((item) {
          final transaction = item['transaction'] as SupplierTransaction;
          final balance = item['balance'] as double;

          return pw.TableRow(
            children: [
              _buildTableCell(_formatDate(transaction.date), ttf),
              _buildTableCell(transaction.description, ttf),
              _buildTableCell(transaction.referenceNumber, ttf),
              _buildTableCell(
                  transaction.debitAmount > 0
                      ? transaction.debitAmount.toStringAsFixed(2)
                      : '-',
                  ttf),
              _buildTableCell(
                  transaction.creditAmount > 0
                      ? transaction.creditAmount.toStringAsFixed(2)
                      : '-',
                  ttf),
              _buildTableCell(balance.toStringAsFixed(2), ttf),
            ],
          );
        }).toList(),
      ],
    );
  }

  static pw.Widget _buildTableCell(String text, pw.Font ttf,
      {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: ttf,
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isHeader ? PdfColors.black : PdfColors.grey800,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static pw.Widget _buildNewSummarySection(
    double totalDebit,
    double totalCredit,
    double finalBalance,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200, width: 1),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص الحساب',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'إجمالي المستحق (له):',
                style: pw.TextStyle(font: ttf, fontSize: 12),
              ),
              pw.Text(
                '${totalDebit.toStringAsFixed(2)} ريال',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'إجمالي المدفوع (عليه):',
                style: pw.TextStyle(font: ttf, fontSize: 12),
              ),
              pw.Text(
                '${totalCredit.toStringAsFixed(2)} ريال',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
            width: double.infinity,
            height: 1,
            color: PdfColors.blue300,
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'الرصيد النهائي:',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.Text(
                '${finalBalance.toStringAsFixed(2)} ريال',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: finalBalance > 0 ? PdfColors.red : PdfColors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  static pw.Widget buildPageFooter(pw.Context context, pw.Font ttf) {
    return pw.Container(
      alignment: pw.Alignment.center,
      margin: const pw.EdgeInsets.only(top: 10),
      child: pw.Text(
        'صفحة ${context.pageNumber} من ${context.pagesCount}',
        style: pw.TextStyle(
          font: ttf,
          fontSize: 10,
          color: PdfColor.fromHex('#666666'),
        ),
      ),
    );
  }
}
