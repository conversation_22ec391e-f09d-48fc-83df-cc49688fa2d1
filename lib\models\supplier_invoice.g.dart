// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supplier_invoice.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SupplierInvoiceItemAdapter extends TypeAdapter<SupplierInvoiceItem> {
  @override
  final int typeId = 10;

  @override
  SupplierInvoiceItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SupplierInvoiceItem(
      product: fields[0] as Product,
      quantity: fields[1] as int,
      totalPrice: fields[2] as double,
      unitCost: fields[3] as double,
    );
  }

  @override
  void write(BinaryWriter writer, SupplierInvoiceItem obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.product)
      ..writeByte(1)
      ..write(obj.quantity)
      ..writeByte(2)
      ..write(obj.totalPrice)
      ..writeByte(3)
      ..write(obj.unitCost);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SupplierInvoiceItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SupplierInvoiceAdapter extends TypeAdapter<SupplierInvoice> {
  @override
  final int typeId = 11;

  @override
  SupplierInvoice read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SupplierInvoice(
      id: fields[0] as String?,
      supplier: fields[1] as Supplier,
      items: (fields[2] as List).cast<SupplierInvoiceItem>(),
      date: fields[3] as DateTime,
      totalAmount: fields[4] as double,
      paidAmount: fields[5] as double?,
      isPaid: fields[6] as bool,
      notes: fields[7] as String?,
      invoiceNumber: fields[8] as String?,
      collectedAmount: fields[9] == null ? 0.0 : fields[9] as double,
    );
  }

  @override
  void write(BinaryWriter writer, SupplierInvoice obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.supplier)
      ..writeByte(2)
      ..write(obj.items)
      ..writeByte(3)
      ..write(obj.date)
      ..writeByte(4)
      ..write(obj.totalAmount)
      ..writeByte(5)
      ..write(obj.paidAmount)
      ..writeByte(6)
      ..write(obj.isPaid)
      ..writeByte(7)
      ..write(obj.notes)
      ..writeByte(8)
      ..write(obj.invoiceNumber)
      ..writeByte(9)
      ..write(obj.collectedAmount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SupplierInvoiceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
