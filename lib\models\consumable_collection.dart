import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'consumable.dart';

part 'consumable_collection.g.dart';

@HiveType(typeId: 8)
class ConsumableCollection {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  List<Consumable> consumables;

  @HiveField(3)
  DateTime createdAt;

  @HiveField(4)
  DateTime updatedAt;

  ConsumableCollection({
    String? id,
    required this.name,
    required this.consumables,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // Calculate total price of all consumables in the collection
  double get totalPrice {
    return consumables.fold(0.0, (sum, consumable) => sum + consumable.price);
  }

  // Get count of consumables in the collection
  int get itemCount {
    return consumables.length;
  }

  // Create a copy of the collection with updated fields
  ConsumableCollection copyWith({
    String? id,
    String? name,
    List<Consumable>? consumables,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ConsumableCollection(
      id: id ?? this.id,
      name: name ?? this.name,
      consumables: consumables ?? List.from(this.consumables),
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // Convert to JSON for debugging/export
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'consumables': consumables.map((c) => c.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'totalPrice': totalPrice,
      'itemCount': itemCount,
    };
  }

  @override
  String toString() {
    return 'ConsumableCollection(id: $id, name: $name, itemCount: $itemCount, totalPrice: $totalPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConsumableCollection && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
