import 'package:flutter/material.dart';

class AppColors {
  // الألوان الأساسية - الأزرق
  static const Color primaryBlue = Color(0xFF2196F3); // الأزرق الرئيسي
  static const Color primaryBlueDark = Color(0xFF1976D2); // الأزرق الداكن
  static const Color primaryBlueLight = Color(0xFF64B5F6); // الأزرق الفاتح

  // الألوان الثانوية المتناسقة
  static const Color accentTeal = Color(0xFF26A69A); // الأزرق المخضر
  static const Color accentCyan = Color(0xFF00BCD4); // السيان
  static const Color accentIndigo = Color(0xFF3F51B5); // النيلي

  // الألوان المحايدة
  static const Color neutralGrey = Color(0xFF9E9E9E); // الرمادي
  static const Color lightGrey = Color(0xFFE0E0E0); // الرمادي الفاتح
  static const Color darkGrey = Color(0xFF424242); // الرمادي الداكن
  static const Color offWhite = Color(0xFFFAFAFA); // الأبيض المكسور

  // الألوان التكميلية
  static const Color warningOrange = Color(0xFFFF9800); // البرتقالي للتحذيرات
  static const Color successGreen = Color(0xFF4CAF50); // الأخضر للنجاح
  static const Color errorRed = Color(0xFFF44336); // الأحمر للأخطاء

  // الألوان الناعمة للخلفيات
  static const Color backgroundBlue = Color(0xFFE3F2FD); // خلفية زرقاء ناعمة
  static const Color backgroundTeal =
      Color(0xFFE0F2F1); // خلفية زرقاء مخضرة ناعمة
  static const Color backgroundGrey = Color(0xFFF5F5F5); // خلفية رمادية ناعمة

  // Backward compatibility aliases
  static const Color primary = primaryBlue;
  static const Color primaryLight = primaryBlueLight;
  static const Color primaryDark = primaryBlueDark;
  static const Color accent = accentTeal;
  static const Color secondary = accentCyan;
  static const Color background = backgroundGrey;
  static const Color surface = offWhite;
  static const Color surfaceVariant = Color(0xFFF8F9FA);
  static const Color textPrimary = darkGrey;
  static const Color textSecondary = neutralGrey;
  static const Color textHint = lightGrey;
  static const Color success = successGreen;
  static const Color warning = warningOrange;
  static const Color error = errorRed;
}
