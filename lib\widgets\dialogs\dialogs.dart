import 'package:flutter/material.dart';
import 'add_consumable_dialog.dart';

export 'add_product_dialog.dart';
export 'add_customer_dialog.dart';
export 'add_invoice_dialog.dart';
export 'add_product_to_invoice_dialog.dart';
export 'add_consumable_dialog.dart';

// Helper function to show add consumable dialog
void showAddConsumableDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const AddConsumableDialog(),
  );
}
