import 'package:alzobidi/widgets/dialogs/add_invoice_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubits/invoices_cubit.dart';
import '../cubits/supplier_invoices_cubit.dart';
import '../cubits/returns_cubit.dart';
import '../cubits/payment_records_cubit.dart';
import '../models/invoice.dart';
import '../models/supplier_invoice.dart';
import '../l10n/app_localizations.dart';
import '../widgets/invoices/invoices.dart';
import '../widgets/dialogs/add_supplier_invoice_dialog.dart';
import '../constants/app_colors.dart';

class InvoicesPage extends StatefulWidget {
  const InvoicesPage({super.key});

  @override
  State<InvoicesPage> createState() => _InvoicesPageState();
}

class _InvoicesPageState extends State<InvoicesPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Initialize invoices with sequential numbering
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<InvoicesCubit>().initializeInvoices();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          localizations.invoices,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryBlue,
        elevation: 0,
        centerTitle: true,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) {
              if (value == 'assign_numbers') {
                _assignSequentialNumbers();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'assign_numbers',
                child: Row(
                  children: [
                    Icon(Icons.format_list_numbered,
                        color: AppColors.primaryBlue),
                    SizedBox(width: 8),
                    Text('تطبيق الترقيم التسلسلي'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'فواتير العملاء'),
            Tab(text: 'فواتير الموردين'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCustomerInvoicesTab(context),
          _buildSupplierInvoicesTab(context),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Show FAB only on supplier invoices tab (index 1)
          if (_tabController.index == 1) {
            return FloatingActionButton.extended(
              onPressed: () => showAddSupplierInvoiceDialog(context: context),
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.receipt_long),
              label: const Text(
                'إضافة فاتورة مورد',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            );
          } else if (_tabController.index == 0) {
            return FloatingActionButton.extended(
              onPressed: () => showAddInvoiceDialog(context),
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.receipt_long),
              label: const Text(
                'إضافة فاتورة عميل',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSupplierInvoicesTab(BuildContext context) {
    return BlocBuilder<SupplierInvoicesCubit, SupplierInvoicesState>(
      builder: (context, state) {
        if (state.supplierInvoicesMap.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.business,
                  size: 64,
                  color: AppColors.neutralGrey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد فواتير موردين',
                  style: TextStyle(
                    fontSize: 18,
                    color: AppColors.neutralGrey,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: state.supplierInvoicesMap.length,
          itemBuilder: (context, index) {
            final supplierName =
                state.supplierInvoicesMap.keys.elementAt(index);
            final supplierInvoices = state.supplierInvoicesMap[supplierName]!;
            return _buildSupplierInvoicesGroup(
                context, supplierName, supplierInvoices);
          },
        );
      },
    );
  }

  Widget _buildCustomerInvoicesTab(BuildContext context) {
    return BlocBuilder<InvoicesCubit, InvoicesState>(
      builder: (context, state) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: state.customerInvoices.length,
          itemBuilder: (context, index) {
            final customerName = state.customerInvoices.keys.elementAt(index);
            final invoices = state.customerInvoices[customerName]!;
            return CustomerInvoicesTile(
              customerName: customerName,
              invoices: invoices,
              onDelete: _confirmDeleteInvoice,
              onTap: _showInvoiceDetails,
            );
          },
        );
      },
    );
  }

  void _showInvoiceDetails(BuildContext context, Invoice invoice) {
    showInvoiceDetailsDialog(
      context: context,
      invoice: invoice,
      onShare: _shareInvoice,
      onDelete: _confirmDeleteInvoice,
    );
  }

  Future<void> _shareInvoice(BuildContext context, Invoice invoice) async {
    final returnsCubit = context.read<ReturnsCubit>();
    final paymentRecordsCubit = context.read<PaymentRecordsCubit>();
    await PdfInvoiceGenerator.generateAndShareInvoice(
      context,
      invoice,
      returnsCubit: returnsCubit,
      paymentRecordsCubit: paymentRecordsCubit,
    );
  }

  void _confirmDeleteInvoice(BuildContext context, Invoice invoice) {
    showDeleteInvoiceDialog(context, invoice);
  }

  Future<void> _assignSequentialNumbers() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
        ),
      ),
    );

    try {
      await context
          .read<InvoicesCubit>()
          .assignSequentialNumbersToExistingInvoices();

      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تطبيق الترقيم التسلسلي بنجاح'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تطبيق الترقيم التسلسلي: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }

  Widget _buildSupplierInvoicesGroup(BuildContext context, String supplierName,
      List<SupplierInvoice> supplierInvoices) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          expansionTileTheme: const ExpansionTileThemeData(
            iconColor: AppColors.primaryBlue,
            collapsedIconColor: AppColors.primaryBlue,
          ),
        ),
        child: ExpansionTile(
          tilePadding: const EdgeInsets.all(16),
          childrenPadding: const EdgeInsets.only(
            left: 16,
            right: 16,
            bottom: 16,
          ),
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.backgroundBlue,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.business,
              color: AppColors.primaryBlue,
              size: 20,
            ),
          ),
          title: Text(
            supplierName,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryBlue,
            ),
          ),
          subtitle: Container(
            margin: const EdgeInsets.only(top: 4),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.backgroundBlue,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${supplierInvoices.length} فاتورة',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.primaryBlue,
              ),
            ),
          ),
          children: supplierInvoices.map((supplierInvoice) {
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () =>
                      _showSupplierInvoiceDetails(context, supplierInvoice),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Icon(
                            Icons.receipt_long,
                            color: AppColors.primaryBlue,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'فاتورة #${supplierInvoice.invoiceNumber ?? supplierInvoice.id.substring(0, 6)}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                              Text(
                                'التاريخ: ${_formatDate(supplierInvoice.date)}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: AppColors.neutralGrey,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.warningOrange.withValues(alpha: 0.1),
                                AppColors.warningOrange.withValues(alpha: 0.05),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppColors.warningOrange
                                  .withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            '${supplierInvoice.totalAmount.toStringAsFixed(2)} ريال',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: AppColors.warningOrange,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showSupplierInvoiceDetails(
      BuildContext context, SupplierInvoice supplierInvoice) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'تفاصيل فاتورة المورد',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryBlue,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'المورد: ${supplierInvoice.supplier.name}',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.darkGrey,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'رقم الفاتورة: ${supplierInvoice.invoiceNumber ?? supplierInvoice.id.substring(0, 8)}',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.darkGrey,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'التاريخ: ${_formatDate(supplierInvoice.date)}',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.darkGrey,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'المبلغ الإجمالي: ${supplierInvoice.totalAmount.toStringAsFixed(2)} ريال',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.warningOrange,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'المبلغ المدفوع: ${(supplierInvoice.paidAmount ?? 0.0).toStringAsFixed(2)} ريال',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.successGreen,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'المبلغ المتبقي: ${supplierInvoice.remainingAmount.toStringAsFixed(2)} ريال',
                style: TextStyle(
                  fontSize: 16,
                  color: supplierInvoice.remainingAmount > 0
                      ? AppColors.errorRed
                      : AppColors.successGreen,
                ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.neutralGrey,
                    ),
                    child: const Text('إغلاق'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
