import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/supplier_payment_record.dart';
import '../models/supplier_invoice.dart';

class SupplierPaymentRecordsCubit extends Cubit<List<SupplierPaymentRecord>> {
  final Box<SupplierPaymentRecord> _supplierPaymentRecordsBox;
  final Box<SupplierInvoice> _supplierInvoicesBox;

  SupplierPaymentRecordsCubit(this._supplierPaymentRecordsBox, this._supplierInvoicesBox)
      : super(_supplierPaymentRecordsBox.values.toList());

  // Add a new supplier payment record
  Future<void> addSupplierPaymentRecord(SupplierPaymentRecord paymentRecord) async {
    try {
      await _supplierPaymentRecordsBox.add(paymentRecord);
      emit(_supplierPaymentRecordsBox.values.toList());
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  // Get all payment records for a specific supplier
  List<SupplierPaymentRecord> getPaymentRecordsForSupplier(String supplierId) {
    return _supplierPaymentRecordsBox.values
        .where((record) => record.supplierId == supplierId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first
  }

  // Get all payment records for a specific supplier invoice
  List<SupplierPaymentRecord> getPaymentRecordsForSupplierInvoice(String invoiceId) {
    return _supplierPaymentRecordsBox.values
        .where((record) => record.invoiceId == invoiceId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first
  }

  // Get payment records within a date range
  List<SupplierPaymentRecord> getPaymentRecordsInDateRange(
      DateTime startDate, DateTime endDate) {
    return _supplierPaymentRecordsBox.values
        .where((record) =>
            record.timestamp.isAfter(startDate) &&
            record.timestamp.isBefore(endDate))
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first
  }

  // Get total payment amount for a supplier
  double getTotalPaymentAmountForSupplier(String supplierId) {
    return _supplierPaymentRecordsBox.values
        .where((record) => record.supplierId == supplierId)
        .fold(0.0, (sum, record) => sum + record.amount);
  }

  // Get total payment amount for a supplier invoice
  double getTotalPaymentAmountForSupplierInvoice(String invoiceId) {
    return _supplierPaymentRecordsBox.values
        .where((record) => record.invoiceId == invoiceId)
        .fold(0.0, (sum, record) => sum + record.amount);
  }

  // Get total collected amount for a supplier from invoices
  double getTotalCollectedAmountForSupplier(String supplierId) {
    return _supplierInvoicesBox.values
        .where((invoice) => invoice.supplier.name == supplierId)
        .fold(0.0, (sum, invoice) => sum + invoice.collectedAmount);
  }

  // Delete a supplier payment record
  Future<void> deleteSupplierPaymentRecord(SupplierPaymentRecord paymentRecord) async {
    try {
      await paymentRecord.delete();
      emit(_supplierPaymentRecordsBox.values.toList());
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  // Clear all supplier payment records (for testing/reset purposes)
  Future<void> clearAllSupplierPaymentRecords() async {
    try {
      await _supplierPaymentRecordsBox.clear();
      emit([]);
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  // Get payment records for multiple suppliers
  List<SupplierPaymentRecord> getPaymentRecordsForSuppliers(List<String> supplierIds) {
    return _supplierPaymentRecordsBox.values
        .where((record) => supplierIds.contains(record.supplierId))
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first
  }

  // Get total payments made in a specific time period
  double getTotalPaymentsInPeriod(DateTime startDate, DateTime endDate) {
    return _supplierPaymentRecordsBox.values
        .where((record) =>
            record.timestamp.isAfter(startDate) &&
            record.timestamp.isBefore(endDate))
        .fold(0.0, (sum, record) => sum + record.amount);
  }

  // Reload data from box
  void loadSupplierPaymentRecords() {
    emit(_supplierPaymentRecordsBox.values.toList());
  }
}
