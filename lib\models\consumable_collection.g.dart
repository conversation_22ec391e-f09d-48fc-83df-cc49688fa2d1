// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consumable_collection.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ConsumableCollectionAdapter extends TypeAdapter<ConsumableCollection> {
  @override
  final int typeId = 8;

  @override
  ConsumableCollection read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ConsumableCollection(
      id: fields[0] as String?,
      name: fields[1] as String,
      consumables: (fields[2] as List).cast<Consumable>(),
      createdAt: fields[3] as DateTime?,
      updatedAt: fields[4] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, ConsumableCollection obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.consumables)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConsumableCollectionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
