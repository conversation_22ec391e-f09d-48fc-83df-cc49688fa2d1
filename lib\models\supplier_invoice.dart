import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'supplier.dart';
import 'product.dart';

part 'supplier_invoice.g.dart';

@HiveType(typeId: 10)
class SupplierInvoiceItem extends HiveObject {
  @HiveField(0)
  Product product;

  @HiveField(1)
  int quantity;

  @HiveField(2)
  double totalPrice;

  @HiveField(3)
  double unitCost;

  SupplierInvoiceItem({
    required this.product,
    required this.quantity,
    required this.totalPrice,
    required this.unitCost,
  });

  // Helper getters
  double get totalCost => quantity * unitCost;
}

@HiveType(typeId: 11)
class SupplierInvoice extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  Supplier supplier;

  @HiveField(2)
  List<SupplierInvoiceItem> items;

  @HiveField(3)
  DateTime date;

  @HiveField(4)
  double totalAmount;

  @HiveField(5)
  double? paidAmount;

  @HiveField(6)
  bool isPaid;

  @HiveField(7)
  String? notes;

  @HiveField(8)
  String? invoiceNumber;

  @HiveField(9, defaultValue: 0.0)
  double collectedAmount;

  SupplierInvoice({
    String? id,
    required this.supplier,
    required this.items,
    required this.date,
    required this.totalAmount,
    this.paidAmount,
    this.isPaid = false,
    this.notes,
    this.invoiceNumber,
    this.collectedAmount = 0.0,
  }) : id = id ?? const Uuid().v4();

  // Helper getters
  double get remainingAmount => totalAmount - (paidAmount ?? 0.0);
  bool get isFullyPaid => isPaid && (paidAmount ?? 0.0) >= totalAmount;
  bool get isPartiallyPaid =>
      (paidAmount ?? 0.0) > 0 && (paidAmount ?? 0.0) < totalAmount;

  // Convert to JSON for debugging/export
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'supplier': supplier.toJson(),
      'items': items
          .map((item) => {
                'product': item.product,
                'quantity': item.quantity,
                'totalPrice': item.totalPrice,
                'unitCost': item.unitCost,
              })
          .toList(),
      'date': date.toIso8601String(),
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'isPaid': isPaid,
      'notes': notes,
      'invoiceNumber': invoiceNumber,
    };
  }

  @override
  String toString() {
    return 'SupplierInvoice{id: $id, supplier: ${supplier.name}, totalAmount: $totalAmount, isPaid: $isPaid}';
  }
}
