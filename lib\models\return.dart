import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'invoice.dart';

part 'return.g.dart';

@HiveType(typeId: 6)
class Return extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String invoiceId;

  @HiveField(2)
  final DateTime returnDate;

  @HiveField(3)
  final List<ReturnItem> items;

  @HiveField(4)
  final double totalReturnAmount;

  @HiveField(5)
  final String? notes;

  Return({
    String? id,
    required this.invoiceId,
    required this.returnDate,
    required this.items,
    required this.totalReturnAmount,
    this.notes,
  }) : id = id ?? const Uuid().v4();

  // Helper getters
  int get totalReturnedQuantity => items.fold(0, (sum, item) => sum + item.returnedQuantity);
  
  // Get return items for a specific product
  List<ReturnItem> getReturnItemsForProduct(String productId) {
    return items.where((item) => item.productId == productId).toList();
  }
}

@HiveType(typeId: 7)
class ReturnItem extends HiveObject {
  @HiveField(0)
  final String productId;

  @HiveField(1)
  final String productCategory;

  @HiveField(2)
  final int returnedQuantity;

  @HiveField(3)
  final double pricePerUnit;

  @HiveField(4)
  final double returnedAmount;

  ReturnItem({
    required this.productId,
    required this.productCategory,
    required this.returnedQuantity,
    required this.pricePerUnit,
    required this.returnedAmount,
  });
}
