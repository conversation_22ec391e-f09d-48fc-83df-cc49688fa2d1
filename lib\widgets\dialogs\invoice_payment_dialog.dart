import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../l10n/app_localizations.dart';
import '../../constants/app_colors.dart';

class InvoicePaymentDialog extends StatefulWidget {
  final double totalAmount;
  final double currentPaidAmount;
  final Function(double) onPaymentAdded;

  const InvoicePaymentDialog({
    super.key,
    required this.totalAmount,
    required this.currentPaidAmount,
    required this.onPaymentAdded,
  });

  @override
  State<InvoicePaymentDialog> createState() => _InvoicePaymentDialogState();
}

class _InvoicePaymentDialogState extends State<InvoicePaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();

  double get remainingAmount => widget.totalAmount - widget.currentPaidAmount;

  @override
  void initState() {
    super.initState();
    // Pre-fill with remaining amount (but not more than what's left to pay)
    final maxPayment = widget.totalAmount - widget.currentPaidAmount;
    _amountController.text = maxPayment.toStringAsFixed(2);
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void _addPayment() {
    if (_formKey.currentState?.validate() ?? false) {
      final amount = double.parse(_amountController.text);
      widget.onPaymentAdded(amount);
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: const EdgeInsets.all(24.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.0),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              AppColors.backgroundBlue,
            ],
          ),
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue,
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    child: const Icon(
                      Icons.payment,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      localizations.addPayment,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Payment Summary
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          localizations.totalAmount,
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          '${widget.totalAmount.toStringAsFixed(2)} SAR',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          localizations.paidAmount,
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          '${widget.currentPaidAmount.toStringAsFixed(2)} SAR',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppColors.successGreen,
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          localizations.remainingBalance,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${remainingAmount.toStringAsFixed(2)} SAR',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: remainingAmount > 0
                                ? AppColors.errorRed
                                : AppColors.successGreen,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Payment Amount Input
              Form(
                key: _formKey,
                child: TextFormField(
                  controller: _amountController,
                  decoration: InputDecoration(
                    labelText: localizations.paymentVoucher,
                    prefixIcon: const Icon(Icons.attach_money,
                        color: Color(0xFF8B4513)),
                    suffixText: 'SAR',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.0),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 16.0,
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations.pleaseEnterAmount;
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return localizations.pleaseEnterValidAmount;
                    }
                    // Check if payment would exceed total invoice amount
                    if (widget.currentPaidAmount + amount >
                        widget.totalAmount) {
                      return localizations.amountExceedsTotal;
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(height: 24),

              // Quick Amount Buttons
              Wrap(
                spacing: 8,
                children: [
                  if (remainingAmount > 0)
                    _buildQuickAmountButton(
                        remainingAmount, localizations.remainingBalance),
                  _buildQuickAmountButton(100, '100 SAR'),
                  _buildQuickAmountButton(200, '200 SAR'),
                  _buildQuickAmountButton(500, '500 SAR'),
                ],
              ),
              const SizedBox(height: 24),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                        side: const BorderSide(color: AppColors.primaryBlue),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                      ),
                      child: Text(
                        localizations.cancel,
                        style: const TextStyle(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _addPayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                      ),
                      child: Text(
                        localizations.addPayment,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickAmountButton(double amount, String label) {
    // Check if this amount would exceed the remaining balance
    final wouldExceedTotal =
        widget.currentPaidAmount + amount > widget.totalAmount;

    return OutlinedButton(
      onPressed: wouldExceedTotal
          ? null
          : () {
              _amountController.text = amount.toStringAsFixed(2);
            },
      style: OutlinedButton.styleFrom(
        side: BorderSide(
          color:
              wouldExceedTotal ? AppColors.neutralGrey : AppColors.primaryBlue,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        label,
        style: TextStyle(
          color:
              wouldExceedTotal ? AppColors.neutralGrey : AppColors.primaryBlue,
          fontSize: 12,
        ),
      ),
    );
  }
}
